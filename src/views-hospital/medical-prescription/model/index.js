import {
    AdviceRuleType,
    AdviceRuleTypeMapTreatmentType,
    GoodsTypeIdMapAdviceRuleType,
    MedicalAdviceTypeEnum,
    TreatmentTypeEnum,
    ST_FREQ,
    CustomTypeIdEnum,
    SingleDosageUnitType,
    DoctorMedicalPrescriptionTypeEnum,
    FirstExecuteFreqList,
    ExecuteTimesByFreq,
} from '@/views-hospital/medical-prescription/utils/constants.js';
import WesternMedicineConfig from 'assets/configure/western-medicine-config';

import { createGUID } from 'utils/index.js';
import { parseTime } from '@abc/utils-date';
import clone from 'utils/clone';
import {
    calcDaysByTimeDifference, calcStopTime, getFreqInfo, getFreqInfoByFirst,
} from '@/views-hospital/medical-prescription/utils/format-advice.js';
import { OutpatientChargeTypeEnum } from 'views/outpatient/constants.js';
const TRANSFER_HOSPITAL = '00000000000000000000000000000033'; // 转院类型goodsID

import executeTime from 'store/modules/execute-time.js';
import { calcChineseStopTime } from '@/views-hospital/medical-prescription/utils/chinese-calcuelate';
import { GoodsTypeIdEnum } from '@abc/constants';
import { BizRelevantIdEnum } from '@/views-hospital/settings-common/frames/doctor-order/nurse/constants';

export class MedicalPrescriptionService {
    static getSheetStruct() {
        return {
            adviceGroups: MedicalPrescriptionService.getGroupItem(),
            patientOrderId: '',
            examApplySheetReqs: [],
        };
    }


    static handleModelGroup(list, allExecuteTimeList) {
        if (list?.length) {
            const handleList = list.map((item) => {
                const keyId = createGUID();
                const startTime = parseTime(new Date(), 'y-m-d h:i', true);
                let stopTime = '';
                let type = item.advices?.[0]?.type;
                // eslint-disable-next-line prefer-const
                let {
                    days ,freq,
                } = item;
                const {
                    usage, dispenseType,
                } = item;
                // 中药饮片需要计算出停止时间
                const adviceRuleType = item.advices[0]?.adviceRule?.type;
                if (adviceRuleType === AdviceRuleType.CHINESE_MEDICINE_TABLETS) {
                    stopTime = calcChineseStopTime(item);
                }
                // 模板只保存了开始时间和天数, 使用模板时需要根据这俩计算出停止时间
                if (days) {
                    stopTime = calcStopTime(startTime, days);
                }
                const freqInfo = getFreqInfoByFirst(allExecuteTimeList, freq, true);
                const deviceType = item.advices[0]?.adviceRule?.ruleItems[0]?.goodsExamDeviceType || 0;
                const extendSpec = item.advices[0]?.adviceRule?.ruleItems[0]?.goodsExtendSpec || '0';
                const advices = clone(item.advices)?.map((i) => {
                    const adviceRule = i?.adviceRule || {};
                    adviceRule.items = [];
                    adviceRule?.ruleItems?.forEach((goods) => {
                        const newGoods = {
                            ...goods,
                            typeId: goods.goodsTypeId,
                            type: goods.goodsType,
                            subType: goods.goodsSubType,
                        };
                        adviceRule.items.push(MedicalPrescriptionService.getAdviceGoodsItem(newGoods));
                    });
                    const adviceRuleType = MedicalPrescriptionService.getAdviceRuleType(adviceRule.items[0]); // 医嘱规则类型
                    const diagnosisTreatmentType = MedicalPrescriptionService.getDiagnosisTreatmentType(adviceRuleType);
                    if (MedicalPrescriptionService.isOnlyOneTimeTreatmentType(diagnosisTreatmentType)) {
                        days = 1;
                        type = MedicalAdviceTypeEnum.ONE_TIME;
                        stopTime = '';
                        freq = ST_FREQ;
                    }
                    adviceRule.keyId = createGUID();
                    let consultationSheet = null;
                    if (adviceRuleType === AdviceRuleType.CONSULTATION) {
                        consultationSheet = {
                            beginTime: '',
                            diseaseDesc: '',
                            isUrgent: 0,
                            location: '',
                            participants: [],
                            preDiagnosis: [],
                            purpose: '',
                        };
                        adviceRule.remark = '';
                    }
                    // 手术关联单清空
                    adviceRule.applySheetSourceId = '';
                    // 塞入会诊对象
                    adviceRule.consultationSheet = consultationSheet;
                    return {
                        adviceRule,
                        diagnosisTreatmentType,
                        tagTypes: [], // 医嘱级别的tag类型（麻醉、精一、精二、毒）
                    };
                });
                return {
                    ...item,
                    advices,
                    keyId,
                    days, // 天数
                    freq, // 频率
                    freqInfo, // 频率信息
                    startTime, // 开始时间
                    stopTime, // 结束时间
                    type, // 临时长期
                    usage, // 用法
                    deviceType,
                    extendSpec,
                    dispenseType,
                    tagTypes: [],
                };
            });
            return handleList;
        }
        return [];
    }
    // 初始化分组
    static getGroupItem(goods) {
        const keyId = createGUID();
        const startTime = parseTime(new Date(), 'y-m-d h:i', true);
        let stopTime = '';
        let days = '';
        let type = MedicalAdviceTypeEnum.LONG_TIME;
        let freq = '';
        let freqInfo = {
            dailyTimings: [],
            firstDayFrequency: 0,
            description: '',
            firstDayTimings: [],
            intervalTime: {
                day: 0,
                month: 0,
                week: 0,
            },
        };
        const adviceRuleType = MedicalPrescriptionService.getAdviceRuleType(goods); // 医嘱规则类型
        const diagnosisTreatmentType = MedicalPrescriptionService.getDiagnosisTreatmentType(adviceRuleType); // 医嘱类型


        if (MedicalPrescriptionService.isOnlyOneTimeTreatmentType(diagnosisTreatmentType)) {
            days = 1;
            type = MedicalAdviceTypeEnum.ONE_TIME;
            stopTime = '';
            freq = ST_FREQ;
            freqInfo = getFreqInfo(executeTime?.state?.allExecuteTimeList, freq);
        }
        return {
            keyId,
            advices: [MedicalPrescriptionService.getAdviceItem(goods, null)],
            days, // 天数
            freq, // 频率
            freqInfo, // 频率信息
            startTime, // 开始时间
            stopTime, // 结束时间
            type, // 临时长期
            usage: '', // 用法
            ivgtt: '',// 输液滴速
            ivgttUnit: '',//滴速单位
            deviceType: goods?.deviceType || goods?.deviceInfo?.deviceType || 0, // 检查检验需要
            extendSpec: goods?.extendSpec || '',
            tagTypes: [],
            dispenseType: null, // 发药类型
        };
    }

    static getAdviceItem(goods, keyId = null) {
        const adviceRuleType = MedicalPrescriptionService.getAdviceRuleType(goods); // 医嘱规则类型
        const diagnosisTreatmentType = MedicalPrescriptionService.getDiagnosisTreatmentType(adviceRuleType); // 医嘱类型
        const adviceRule = MedicalPrescriptionService.getAdviceRuleStruct(goods, keyId);
        return {
            adviceRule, // getAdviceRuleStruct 结构
            diagnosisTreatmentType, // 医嘱类型
            tagTypes: [], // 医嘱级别的tag类型（麻醉、精一、精二、毒）
        };
    }
    static getAdviceRuleStruct(goods = null, oldKeyId = null) {
        const keyId = oldKeyId || createGUID();
        const adviceRuleType = MedicalPrescriptionService.getAdviceRuleType(goods); // 医嘱规则类型

        let dosageCount = '';
        let dosageUnit = goods?.pieceUnit;
        const singleDosageCount = 1;
        let singleDosageUnit = goods?.packageUnit;

        if (MedicalPrescriptionService.isDefaultOneTimeAdviceItem(adviceRuleType)) {
            dosageCount = 1;
            dosageUnit = goods?.packageUnit || '';
        }
        let consultationSheet = null;
        if (adviceRuleType === AdviceRuleType.CONSULTATION) {
            consultationSheet = {
                beginTime: '',
                diseaseDesc: '',
                isUrgent: 0,
                location: '',
                participants: [],
                preDiagnosis: [],
                purpose: '',
            };
        }
        // 如果是药品医嘱，单次剂量量默认是小单位
        if ([AdviceRuleType.WESTERN_MEDICINE].includes(adviceRuleType)) {
            singleDosageUnit = goods?.pieceUnit;
        }
        // 如果是耗材或商品，单次剂量和总剂量：如果可拆零，默认小单位，如果不可拆零，默认大单位
        if ([AdviceRuleType.MEDICINE_MATERIAL, AdviceRuleType.SELF_PRODUCT, AdviceRuleType.HEALTH_MEDICINE, AdviceRuleType.HEALTH_FOOD, AdviceRuleType.OTHER_PRODUCT].includes(adviceRuleType)) {
            if (goods.pieceUnit && goods.packageUnit !== goods.pieceUnit && goods.dismounting) {
                dosageUnit = goods.pieceUnit;
                singleDosageUnit = goods.pieceUnit;
            }
        }
        const {
            pieceUnit, packageUnit,pharmacyNo,pharmacyType,
        } = goods || {};
        // 拆零不考虑是否拆零售卖这个字段，如果选择了 pieceUnit 就是拆零
        const dosageIsDismounting = +(dosageUnit === pieceUnit && dosageUnit !== packageUnit);

        return {
            keyId,
            isChecked: false,
            astFlag: null, // 皮试
            dailyDosage: '', // 每日剂量
            days: '', // 天数
            deathTime: '', // 死亡时间
            dosageCount, // 总量
            dosageUnit, // 总量单位
            dosageIsDismounting, // 总量是否拆零
            examApplySheetId: '', // 检验申请单
            isNeedProcess: 0, // 是否需要代加工
            items: goods ? [MedicalPrescriptionService.getAdviceGoodsItem(goods)] : [],
            name: goods?.medicineCadn || goods?.name, // 名称
            pharmacyNo, // 药房号
            pharmacyType, // 药房类型
            processInfo: {
                displayName: '',
                perDosageBagCount: '',
                remark: '',
                price: '',
                totalBagCount: '',
                usageType: '',
                usageSubType: '',
                feeCategoryId: null,
            }, // 加工信息
            remark: '',//备注
            singleDosageCount,//单次用量
            singleDosageUnit,//单次用量单位
            singleDosageUnitType: singleDosageUnit === goods?.pieceUnit ? SingleDosageUnitType.PIECE : SingleDosageUnitType.PACKAGE,
            stopLongAdviceTime: '',//出院停长医嘱时/
            dischargeHospitalTime: '', // 出院时间
            consultationSheet,
            transferToDepartment: '',//转入科室
            treatmentSites: [],//治疗部位穴位
            type: adviceRuleType, // 医嘱类型
            // 关联手术申请单, 目前只有手术医嘱在用
            applySheetSourceId: '',
            // 是否停止该医嘱之前的长期医嘱, 目前只有术后医嘱在用
            isStopBeforeStartLongAdvice: 1,

            singleDispenseCount: '', // 单次发药数量
            singleDispenseUnit: dosageUnit, // 单次发药单位
            singleDispenseIsDismounting: dosageIsDismounting, // 单次发药是否拆零
        };
    }

    static getAdviceGoodsItem(goods) {
        const {
            id,
            goodsId,
            goodsName = '',
            goodsSpec = '',
            dismounting,
            displaySpec,
            pharmacyNo,
            pharmacyType,
            remark,
            name,
            medicineCadn,
            medicineDosageNum,
            medicineDosageUnit,
            componentContentNum,
            componentContentUnit,
            packageUnit,
            pieceNum,
            pieceUnit,
            typeId,
            type,
            subType,
            customTypeId,
            stockPackageCount,
            stockPieceCount,
            cMSpec,
            feeComposeList,
            noStocks,
            unit,
            unitCount,
            lastPackageCostPrice,
            piecePrice,
            packagePrice,
            hospitalNeedExecutive,
            eqCoefficient,
            eqUnitCount,
        } = goods;

        return {
            id: goodsId || id,
            keyId: createGUID(),
            goodsId: goodsId || id,
            dismounting,
            pharmacyNo,
            pharmacyType,
            remark,
            displaySpec,
            stockPackageCount,
            stockPieceCount,
            name: name || goodsName,
            medicineCadn: medicineCadn || goodsName,
            medicineDosageNum,
            medicineDosageUnit,
            componentContentNum,
            componentContentUnit,
            packageUnit: packageUnit || goodsSpec,
            pieceNum,
            pieceUnit,
            typeId,
            customTypeId,
            type,
            subType,
            cMSpec,
            chargeFlag: OutpatientChargeTypeEnum.DEFAULT, // 是否自备
            feeComposeList: feeComposeList || [],
            noStocks,
            unit,
            eqCoefficient,
            unitCount: unitCount || '',
            eqUnitCount: eqUnitCount || '',
            lastPackageCostPrice,
            piecePrice,
            packagePrice,
            hospitalNeedExecutive,
            productInfo: goods.productInfo || goods,
        };
    }

    // 临时医嘱
    static isDefaultOneTimeAdviceItem(adviceRuleType) {
        const oneTimeAdviceTypeList = [
            AdviceRuleType.ASSAY,
            AdviceRuleType.INSPECTION,
            AdviceRuleType.TRANSFER_DEPARTMENT,
            AdviceRuleType.DISCHARGE_WITH_MEDICINE,
            AdviceRuleType.TRANSFER_WITH_MEDICINE,
            AdviceRuleType.SURGERY,
            AdviceRuleType.POSTOPERATIVE,
            AdviceRuleType.MEDICINE_MATERIAL,
            AdviceRuleType.SELF_PRODUCT,
            AdviceRuleType.HEALTH_MEDICINE,
            AdviceRuleType.HEALTH_FOOD,
            AdviceRuleType.OTHER_PRODUCT,
        ];

        return oneTimeAdviceTypeList.findIndex((type) => {
            return type === adviceRuleType;
        }) > -1;
    }

    // 临时医嘱
    static isOnlyOneTimeTreatmentType(treatmentType) {
        const adviceTypeList = [
            TreatmentTypeEnum.INSPECTION,
            TreatmentTypeEnum.ASSAY,
            TreatmentTypeEnum.DISCHARGE_WITH_MEDICINE,
            TreatmentTypeEnum.TRANSFER_DEPARTMENT,
            TreatmentTypeEnum.TRANSFER_WITH_MEDICINE,
            TreatmentTypeEnum.CONSULTATION,
            TreatmentTypeEnum.SURGERY,
            TreatmentTypeEnum.MATERIALS,
        ];
        return adviceTypeList.findIndex((item) => item === treatmentType) > -1;
    }

    static getAdviceRuleType(goods) {
        if (!goods) return null;
        const {
            typeId, customTypeId, goodsId,
        } = goods;
        if ([`${CustomTypeIdEnum.NURSE_NURSE_LEVEL}`].includes(customTypeId)) {
            return GoodsTypeIdMapAdviceRuleType[customTypeId];
        }
        if (typeId === GoodsTypeIdEnum.NURSE && goods.bizRelevantId && goods.bizRelevantId !== BizRelevantIdEnum.GENERAL_NURSING) {
            return AdviceRuleType.NURSE_LEVEL;
        }
        if (goodsId === TRANSFER_HOSPITAL) {
            return GoodsTypeIdMapAdviceRuleType[CustomTypeIdEnum.TRANS_LEVEL];
        }
        return GoodsTypeIdMapAdviceRuleType[typeId];
    }

    static getDiagnosisTreatmentType (adviceItemType) {
        return AdviceRuleTypeMapTreatmentType[adviceItemType];
    }


    static getHandWrittenGroupItem() {
        const keyId = createGUID();
        const adviceRule = this.getAdviceRuleStruct(null); // getAdviceRuleStruct 结构;
        // 手写医嘱去掉默认单次用量
        adviceRule.singleDosageCount = '';
        return {
            keyId,
            advices: [{
                adviceRule,
                diagnosisTreatmentType: '', // 医嘱类型
                createdType: DoctorMedicalPrescriptionTypeEnum.WRITTEN,
                tagTypes: [], // 医嘱级别的tag类型（麻醉、精一、精二、毒）
            }],
            days: '', // 天数
            freq: '', // 频率
            freqInfo: {}, // 频率信息
            startTime: parseTime(new Date(), 'y-m-d h:i', true), // 开始时间
            stopTime: '', // 结束时间
            type: MedicalAdviceTypeEnum.ONE_TIME, // 临时长期
            usage: '', // 用法
            ivgtt: '',// 输液滴速
            ivgttUnit: '',//滴速单位
            deviceType: 0, // 检查检验需要
            extendSpec: '',
            tagTypes: [],
        };
    }

    /**
     * 出院带药医嘱修改发药总量反算天数
     */
    static calcMedicineDays(params) {
        const {
            advice, dosageCount, dosageUnit, freq, freqInfo,
        } = params;

        const {
            singleDosageCount, singleDosageUnit,
        } = advice;

        let days = '';

        if (!singleDosageCount || !singleDosageUnit || !dosageCount || !dosageUnit || !freq) return days;
        if (!advice.items || !advice.items[0]) return days;

        const {
            componentContentUnit,
            componentContentNum,
            medicineDosageUnit,
            pieceUnit,
            pieceNum,
            medicineDosageNum,
            packageUnit,
        } = advice.items[0];

        const _unitArr = ['ml', 'mg', 'g', 'IU'];

        let count = 1;
        if (singleDosageUnit === componentContentUnit) {
            if (dosageUnit === packageUnit) {
                count = dosageCount * pieceNum * componentContentNum / singleDosageCount;
            } else if (dosageUnit === pieceUnit) {
                count = dosageCount * componentContentNum / singleDosageCount;
            } else if (dosageUnit === medicineDosageUnit) {
                count = dosageCount / medicineDosageNum * componentContentNum / singleDosageCount;
            } else {
                count = dosageCount / singleDosageCount;
            }
        } else if (singleDosageUnit === medicineDosageUnit) {
            if (dosageUnit === packageUnit) {
                if ((_unitArr.includes(medicineDosageUnit) || !medicineDosageUnit) && _unitArr.includes(pieceUnit)) {
                    count = dosageCount * medicineDosageNum / singleDosageCount;
                } else {
                    count = dosageCount * pieceNum * medicineDosageNum / singleDosageCount;
                }
            } else if (dosageUnit === pieceUnit) {
                count = dosageCount * medicineDosageNum / singleDosageCount;
            } else if (dosageUnit === componentContentUnit) {
                count = dosageCount / componentContentNum * medicineDosageNum / singleDosageCount;
            } else {
                count = dosageCount / singleDosageCount;
            }
        } else if (singleDosageUnit === pieceUnit) {
            if (dosageUnit === packageUnit) {
                count = dosageCount * pieceNum / singleDosageCount;
            } else if (dosageUnit === pieceUnit) {
                count = dosageCount / singleDosageCount;
            } else if (dosageUnit === medicineDosageUnit) {
                count = dosageCount / medicineDosageNum / singleDosageCount;
            } else {
                count = dosageCount / componentContentNum / singleDosageCount;
            }
        } else {
            if (dosageUnit === packageUnit) {
                count = dosageCount / singleDosageCount;
            } else if (dosageUnit === pieceUnit) {
                count = dosageCount / pieceNum / singleDosageCount;
            } else if (dosageUnit === medicineDosageUnit) {
                count = dosageCount / medicineDosageNum / pieceNum / singleDosageCount;
            } else {
                count = dosageCount / componentContentNum / pieceNum / singleDosageCount;
            }
        }
        count = Math.ceil(count);

        let dailyTimes = ExecuteTimesByFreq[freqInfo.code];
        if (FirstExecuteFreqList.includes(freqInfo.code)) {
            // nid 特殊处理
            if (freqInfo.code === 'nid') {
                dailyTimes = parseInt(freq.match(/^\d+/)?.[0] ?? 5);
            }
            days = count / dailyTimes;
        } else if (freqInfo.code === 'qod') {
            days = count / 2;
        } else if (freqInfo.code === 'qw') {
            days = count / 7;
        } else if (freqInfo.code === 'biw') {
            days = Math.ceil(count / 2) * 7;
        } else if (freqInfo.code === 'qnd') {
            const daysCount = parseInt(freq.match(/\d+/)?.[0] ?? 3);
            days = count * daysCount;
        } else if (freqInfo.code === 'qnw') {
            const weeksCount = parseInt(freq.match(/\d+/)?.[0] ?? 3);
            days = count * 7 * weeksCount;
        }
        days = typeof days === 'number' ? Math.ceil(days) : '';
        return days;
    }

    /**
     * 计算单次用量
     * @param advice
     * @param usage
     * @return {{unit: string, dismounting: number, unitCount: string}}
     */
    static calMedicineCount(advice, usage) {
        const res = {
            unitCount: '',
            unit: '',
            dismounting: 0,
        };
        if (!advice || !advice?.items?.[0]) {
            return res;
        }
        const {
            singleDosageCount, singleDosageUnit,
        } = advice;
        if (!singleDosageUnit) {
            return res;
        }

        const {
            componentContentUnit,
            componentContentNum,
            medicineDosageUnit,
            pieceUnit,
            pieceNum,
            medicineDosageNum,
            packageUnit,
            dismounting: goodsDismounting,
        } = advice.items[0];

        /**
         * @desc 统一将剂量单位开的数量换算成制剂单位的用量
         * @desc 10ml*10支/盒 => 5ml = 0.5支
         * @desc 0.3mg:2ml*10支/盒 => 0.15mg = 1ml
         */

        // 如果单次计量选择了大单位, 则单独处理
        if (singleDosageUnit === packageUnit) {
            res.unitCount = singleDosageCount;
            res.unit = packageUnit;
            res.dismounting = 0;
            return res;
        }

        const _unitArr = ['ml', 'mg', 'g', 'IU'];
        // 默认用节约型计算
        let useStandardCount = false;
        const usageConfig = WesternMedicineConfig.usage.find((it) => {
            return usage === it.name && it.type === 2;
        });
        // 如果是输注用法, 则用标准型计算
        if (usageConfig) {
            useStandardCount = true;
        }
        let preDosageCount = singleDosageCount, preDosageUnit = '';
        if (singleDosageUnit === componentContentUnit) {
            preDosageCount = preDosageCount / componentContentNum;
        } else if (singleDosageUnit === medicineDosageUnit) {
            if (_unitArr.includes(medicineDosageUnit) && _unitArr.includes(pieceUnit)) {
                // case4 0.3mg:2ml/支
                preDosageCount = preDosageCount * pieceNum / medicineDosageNum;
            } else {
                // case1 10ml*10支/盒
                preDosageCount = preDosageCount / medicineDosageNum;
            }
        }
        // 制剂单位不为 ml/mg/g/IU 需要提前ceil
        if (!_unitArr.includes(pieceUnit) && useStandardCount) {
            preDosageCount = Math.ceil(preDosageCount);
        }
        if (goodsDismounting) {
            preDosageUnit = pieceUnit;
        } else {
            preDosageCount /= pieceNum;
            preDosageUnit = packageUnit;

            // 制剂单位是 ml/mg/g/IU 需要算出 包装单位用量后ceil
            if (_unitArr.includes(pieceUnit) && useStandardCount) {
                preDosageCount = Math.ceil(preDosageCount);
            }
        }
        preDosageCount = Math.ceil(preDosageCount);
        if (isNaN(preDosageCount)) {
            preDosageCount = '';
        }
        res.unitCount = preDosageCount;
        res.unit = preDosageUnit;
        res.dismounting = +(goodsDismounting && preDosageUnit === pieceUnit && preDosageUnit !== packageUnit);
        console.log('计算得到的用量', res);
        return res;
    }

    /**
     * 长期医嘱计算全部发药的总量
     * @param params
     * @return {{unit: string, dismounting: number, unitCount: string}}
     */
    static calMedicineCountByDispensingAll(params) {
        const {
            advice, startTime, stopTime, freq, freqInfo, usage, isDischargeAdvice,
        } = params;
        let { days } = params;
        const res = {
            unitCount: '',
            unit: '',
            dismounting: 0,
        };
        if (!advice || !advice?.items?.[0]) {
            return res;
        }
        const {
            singleDosageCount, singleDosageUnit,
        } = advice;
        if (!singleDosageUnit) {
            return res;
        }

        if (!freqInfo || !freqInfo.code) {
            return res;
        }
        if (!days && (!startTime || !stopTime)) {
            return res;
        }
        if (!days && startTime && stopTime) {
            days = calcDaysByTimeDifference(startTime, stopTime);
        }

        const {
            componentContentUnit,
            componentContentNum,
            medicineDosageUnit,
            pieceUnit,
            pieceNum,
            medicineDosageNum,
            packageUnit,
            dismounting: goodsDismounting,
        } = advice.items[0];

        // 如果单次计量选择了大单位, 则单独处理
        if (singleDosageUnit === packageUnit) {
            // 总量单位保持一致,使用大单位
            res.unit = packageUnit;
            res.dismounting = 0;

            let dailyTimes = ExecuteTimesByFreq[freqInfo.code];
            if (FirstExecuteFreqList.includes(freqInfo.code)) {
                // nid 特殊处理
                if (freqInfo.code === 'nid') {
                    dailyTimes = parseInt(freq.match(/^\d+/)?.[0] ?? 5);
                }
                if (isDischargeAdvice) {
                    res.unitCount = dailyTimes * singleDosageCount * days;
                } else {
                    res.unitCount = freqInfo.firstDayFrequency * singleDosageCount + dailyTimes * singleDosageCount * (days - 1);
                }
            } else if (freqInfo.code === 'qod') {
                if (isDischargeAdvice) {
                    res.unitCount = dailyTimes * singleDosageCount * Math.ceil(days / 2);
                } else {
                    res.unitCount = freqInfo.firstDayFrequency * singleDosageCount + dailyTimes * singleDosageCount * Math.ceil((days - 1) / 2);
                }
            } else if (freqInfo.code === 'qw') {
                res.unitCount = dailyTimes * singleDosageCount * Math.ceil(days / 7);
            } else if (freqInfo.code === 'biw') {
                res.unitCount = dailyTimes * singleDosageCount * Math.ceil(days / 3.5);
            } else if (freqInfo.code === 'qnd') {
                const daysCount = parseInt(freq.match(/\d+/)?.[0] ?? 3);
                res.unitCount = dailyTimes * singleDosageCount * Math.ceil(days / daysCount);
            } else if (freqInfo.code === 'qnw') {
                const weeksCount = parseInt(freq.match(/\d+/)?.[0] ?? 3);
                res.unitCount = dailyTimes * singleDosageCount * Math.ceil(days / weeksCount / 7);
            }
            return res;
        }

        const _unitArr = ['ml', 'mg', 'g', 'IU'];
        // 默认节约型计算 计算
        let useStandardCount = false, preSingleDosageCount = singleDosageCount;
        // 获取输注用法 type === 2
        const usageConfig = WesternMedicineConfig.usage.find((it) => {
            return usage === it.name && it.type === 2;
        });
        if (usageConfig) {
            useStandardCount = true;
        }
        if (singleDosageUnit === componentContentUnit) {
            preSingleDosageCount = preSingleDosageCount / componentContentNum;
        } else if (singleDosageUnit === medicineDosageUnit) {
            if (_unitArr.includes(medicineDosageUnit) && _unitArr.includes(pieceUnit)) {
                // case4 0.3mg:2ml/支
                preSingleDosageCount = preSingleDosageCount * pieceNum / medicineDosageNum;
            } else {
                // case1 10ml*10支/盒
                preSingleDosageCount = preSingleDosageCount / medicineDosageNum;
            }
        }
        let unit = '';
        // 制剂单位不为 ml/mg/g/IU 需要提前ceil
        if (!_unitArr.includes(pieceUnit) && useStandardCount) {
            preSingleDosageCount = Math.ceil(preSingleDosageCount);
        }
        if (goodsDismounting) {
            unit = pieceUnit;
        } else {
            preSingleDosageCount /= pieceNum;
            unit = packageUnit;

            // 制剂单位是 ml/mg/g/IU 需要算出 包装单位用量后ceil
            if (_unitArr.includes(pieceUnit) && useStandardCount) {
                preSingleDosageCount = Math.ceil(preSingleDosageCount);
            }
        }

        let dailyTimes = ExecuteTimesByFreq[freqInfo.code];
        let totalCount = preSingleDosageCount;
        if (FirstExecuteFreqList.includes(freqInfo.code)) {
            // nid 特殊处理
            if (freqInfo.code === 'nid') {
                dailyTimes = parseInt(freq.match(/^\d+/)?.[0] ?? 5);
            }
            if (isDischargeAdvice) {
                totalCount = dailyTimes * preSingleDosageCount * days;
            } else {
                totalCount = freqInfo.firstDayFrequency * preSingleDosageCount + dailyTimes * preSingleDosageCount * (days - 1);
            }
        } else if (freqInfo.code === 'qod') {
            if (isDischargeAdvice) {
                totalCount = dailyTimes * preSingleDosageCount * (days / 2);
            } else {
                totalCount = freqInfo.firstDayFrequency * preSingleDosageCount + dailyTimes * preSingleDosageCount * ((days - 1) / 2);
            }
        } else if (freqInfo.code === 'qw') {
            totalCount = dailyTimes * preSingleDosageCount * (days / 7);
        } else if (freqInfo.code === 'biw') {
            totalCount = dailyTimes * preSingleDosageCount * (days / 3.5);
        } else if (freqInfo.code === 'qnd') {
            const daysCount = parseInt(freq.match(/\d+/)?.[0] ?? 3);
            totalCount = dailyTimes * preSingleDosageCount * (days / daysCount);
        } else if (freqInfo.code === 'qnw') {
            const weeksCount = parseInt(freq.match(/\d+/)?.[0] ?? 3);
            totalCount = dailyTimes * preSingleDosageCount * (days / weeksCount / 7);
        }
        const resUnitCount = Math.floor(totalCount * 100);
        res.unitCount = Math.ceil(resUnitCount / 100);
        if (isNaN(res.unitCount)) {
            res.unitCount = '';
        }
        res.unit = unit;
        res.dismounting = +(goodsDismounting && unit === pieceUnit && unit !== packageUnit);
        console.log('计算得到的用量', res);
        return res;
    }

    /**
     * 出院带药类型计算发药总量
     */
    static calMedicineCountByDischarge(params) {
        const {
            advice, days, freq, freqInfo,
        } = params;
        const res = {
            unitCount: '',
            unit: '',
            dismounting: 0,
        };
        if (!advice || !advice?.items?.[0]) {
            return res;
        }
        let {
            singleDosageCount,
        } = advice;
        const { singleDosageUnit } = advice;
        if (!singleDosageUnit) {
            return res;
        }

        if (!freqInfo || !freqInfo.code) {
            return res;
        }
        if (!days) {
            return res;
        }

        const {
            componentContentUnit,
            componentContentNum,
            medicineDosageUnit,
            pieceUnit,
            pieceNum,
            medicineDosageNum,
            packageUnit,
            dismounting: goodsDismounting,
        } = advice.items[0];

        // 如果单次计量选择了大单位, 则单独处理
        if (singleDosageUnit === packageUnit) {
            // 总量单位保持一致,使用大单位
            res.unit = packageUnit;
            res.dismounting = 0;

            let dailyTimes = ExecuteTimesByFreq[freqInfo.code];
            if (FirstExecuteFreqList.includes(freqInfo.code)) {
                // nid 特殊处理
                if (freqInfo.code === 'nid') {
                    dailyTimes = parseInt(freq.match(/^\d+/)?.[0] ?? 5);
                }
                res.unitCount = dailyTimes * singleDosageCount * days;
            } else if (freqInfo.code === 'qod') {
                res.unitCount = dailyTimes * singleDosageCount * Math.ceil(days / 2);
            } else if (freqInfo.code === 'qw') {
                res.unitCount = dailyTimes * singleDosageCount * Math.ceil(days / 7);
            } else if (freqInfo.code === 'biw') {
                res.unitCount = dailyTimes * singleDosageCount * Math.ceil(days / 3.5);
            } else if (freqInfo.code === 'qnd') {
                const daysCount = parseInt(freq.match(/\d+/)?.[0] ?? 3);
                res.unitCount = dailyTimes * singleDosageCount * Math.ceil(days / daysCount);
            } else if (freqInfo.code === 'qnw') {
                const weeksCount = parseInt(freq.match(/\d+/)?.[0] ?? 3);
                res.unitCount = dailyTimes * singleDosageCount * Math.ceil(days / weeksCount / 7);
            }

            return res;
        }

        /**
         * 先计算出单次用量,再换算为总量
         */

        // 大小单位不一致默认小单位
        res.unit = pieceUnit !== packageUnit ? pieceUnit : packageUnit;
        const _unitArr = ['ml', 'mg', 'g', 'IU'];
        if (singleDosageUnit === componentContentUnit) {
            // 开药用的是容量单位，直接除以 因子
            singleDosageCount = singleDosageCount / componentContentNum;
        } else if (singleDosageUnit === medicineDosageUnit) {
            // 开药是剂量单位，
            // 剂量单位不存在，或者满足_unitArr 并且 小单位满足 _unitArr 开药单位默认为大单位
            if ((_unitArr.includes(medicineDosageUnit) || !medicineDosageUnit) && _unitArr.includes(pieceUnit)) {
                // 剂量单位和小单位都是 ml mg g IU , 之间不算是倍数转换，只做比例转换
                // case4 0.3mg * 2ml/支
                singleDosageCount = singleDosageCount * pieceNum / medicineDosageNum;
            } else {
                // 默认走 剂量单位的转换因数
                // case1 10ml*10支/盒
                singleDosageCount = singleDosageCount / medicineDosageNum;
            }
        }

        if ((_unitArr.includes(medicineDosageUnit) || !medicineDosageUnit) && _unitArr.includes(pieceUnit)) {
            res.unit = packageUnit;
        }

        if (res.unit === pieceUnit && !goodsDismounting) {
            res.unit = packageUnit;
        }

        // 向上取整
        // singleDosageCount = Math.ceil(singleDosageCount);
        res.dismounting = Number(res.unit === pieceUnit && pieceUnit !== packageUnit);
        res.unitCount = singleDosageCount;

        // 大单位开药，需要 除以 pieceNum
        if (!res.dismounting && res.unit === packageUnit) {
            res.unitCount = singleDosageCount / pieceNum;
        }

        /**
         * 换算为总量
         */
        const tempUnitCount = res.unitCount;
        let dailyTimes = ExecuteTimesByFreq[freqInfo.code];
        if (FirstExecuteFreqList.includes(freqInfo.code)) {
            // nid 特殊处理
            if (freqInfo.code === 'nid') {
                dailyTimes = parseInt(freq.match(/^\d+/)?.[0] ?? 5);
            }
            res.unitCount = dailyTimes * tempUnitCount * days;
        } else if (freqInfo.code === 'qod') {
            res.unitCount = dailyTimes * tempUnitCount * Math.ceil(days / 2);
        } else if (freqInfo.code === 'qw') {
            res.unitCount = dailyTimes * tempUnitCount * Math.ceil(days / 7);
        } else if (freqInfo.code === 'biw') {
            res.unitCount = dailyTimes * tempUnitCount * Math.ceil(days / 3.5);
        } else if (freqInfo.code === 'qnd') {
            const daysCount = parseInt(freq.match(/\d+/)?.[0] ?? 3);
            res.unitCount = dailyTimes * tempUnitCount * Math.ceil(days / daysCount);
        } else if (freqInfo.code === 'qnw') {
            const weeksCount = parseInt(freq.match(/\d+/)?.[0] ?? 3);
            res.unitCount = dailyTimes * tempUnitCount * Math.ceil(days / weeksCount / 7);
        }

        res.unitCount = Math.ceil(res.unitCount);

        console.log('计算得到的用量', res);
        return res;
    }
}
