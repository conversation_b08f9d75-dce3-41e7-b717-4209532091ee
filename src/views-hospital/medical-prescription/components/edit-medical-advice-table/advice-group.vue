<template>
    <div class="edit-medical-advice-group-wrapper">
        <abc-form-item-group
            v-for="(advice, adviceIndex) in adviceGroup.advices"
            :key="advice.adviceRule.keyId"
            v-bind="advice"
        >
            <component
                :is="getComponent(advice)"
                :freq.sync="adviceGroup.freq"
                :days.sync="adviceGroup.days"
                :type.sync="adviceGroup.type"
                :advice="advice.adviceRule"
                :diagnosis-treatment-type.sync="advice.diagnosisTreatmentType"
                :tag-types.sync="advice.tagTypes"
                :usage.sync="adviceGroup.usage"
                :start-time.sync="adviceGroup.startTime"
                :stop-time.sync="adviceGroup.stopTime"
                :freq-info.sync="adviceGroup.freqInfo"
                :dispense-type.sync="adviceGroup.dispenseType"
                :disabled="disabled"
                :no="no"
                :patient-order-id="patientOrderId"
                :patient="patient"
                :device-type="adviceGroup.deviceType"
                :extend-spec="adviceGroup.extendSpec"
                :group-item="adviceGroup"
                :exam-advice-groups="examAdviceGroups"
                :exam-apply-sheet-reqs="examApplySheetReqs"
                :doctor="doctor"
                :operate-department-id="operateDepartmentId"
                :department-name="departmentName"
                :departments="departments"
                :surgery-reqs="surgeryReqs"
                :is-group-start="adviceGroup.advices.length > 1 && adviceIndex === 0"
                :group-count="adviceGroup.advices.length"
                @openDiagnosis="$emit('openDiagnosis')"
                @refreshCheckExamApplySheetReqs="$emit('refreshCheckExamApplySheetReqs')"
                @addExamApply="(val, type)=>{
                    $emit('addExamApply', val, type);
                }"
                @handeClearSurgeryApplyList="handeClearSurgeryApplyList"
            >
            </component>
        </abc-form-item-group>
        <!--<div v-if="adviceGroup.advices && adviceGroup.advices.length > 1" class="group-line" :style="{ height: `${(adviceGroup.advices.length - 1) * 40 }px` }"></div>-->
    </div>
</template>

<script>
    import WesternAdvice
        from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/advices/western-advice.vue';
    import ExamAdvice from './advices/exam-advice.vue';
    import ChineseAdvice from './advices/chinese-advice.vue';
    import DischargeAdvice from './advices/discharge-advice.vue';
    import TransferAdvice from './advices/transfer-advice.vue';
    import ConsultationAdvice from './advices/consultation-advice.vue';
    import HandWrittenAdvice
        from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/advices/hand-written-advice.vue';
    import TreatmentAdvice
        from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/advices/treatment-advice.vue';
    import SurgeryAdvice
        from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/advices/surgery-advice.vue';
    import {
        AdviceRuleType,
        DoctorMedicalPrescriptionTypeEnum,
    } from '@/views-hospital/medical-prescription/utils/constants.js';
    import MaterialAdvice
        from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/advices/material-adivce.vue';

    export default {
        name: 'AdviceGroup',
        components: {
            WesternAdvice,
            ExamAdvice,
            DischargeAdvice,
            TransferAdvice,
            ConsultationAdvice,
            ChineseAdvice,
            TreatmentAdvice,
            SurgeryAdvice,
        },
        props: {
            adviceGroup: {
                type: Object,
            },
            examAdviceGroups: {
                type: Array,
            },
            no: {
                type: [Number, String],
                default: 0,
            },
            examApplySheetReqs: {
                type: Array,
            },
            disabled: Boolean,
            patientOrderId: {
                type: String,
                default: '',
            },
            patient: {
                type: Object,
                required: true,
            },
            doctor: {
                type: Object,
                default: () => ({}),
            },
            operateDepartmentId: {
                type: String,
                default: '',
            },
            departmentName: {
                type: String,
                default: '',
            },
            departments: {
                type: Array,
                default: () => ([]),
            },
            surgeryReqs: {
                type: Array,
                default: () => ([]),
            },
        },

        methods: {
            getComponent(advice) {
                const {
                    adviceRule,
                    createdType,
                } = advice;
                const { type } = adviceRule;
                // 手写嘱托
                if (createdType === DoctorMedicalPrescriptionTypeEnum.WRITTEN) {
                    return HandWrittenAdvice;
                }
                // 西成药医嘱
                if (type === AdviceRuleType.WESTERN_MEDICINE) {
                    return WesternAdvice;
                }
                // 中药医嘱
                if (advice.adviceRule.type === AdviceRuleType.CHINESE_MEDICINE_TABLETS ||
                    advice.adviceRule.type === AdviceRuleType.CHINESE_MEDICINE_GRANULES) {
                    return ChineseAdvice;
                }
                // 检查检验
                if (advice.adviceRule.type === AdviceRuleType.INSPECTION || advice.adviceRule.type === AdviceRuleType.ASSAY) {
                    return ExamAdvice;
                }
                // 出院 （转院视为出院的一个子类型，本质还是出院）
                if ([AdviceRuleType.DISCHARGE_WITH_MEDICINE, AdviceRuleType.TRANSFER_WITH_MEDICINE].includes(type)) {
                    return DischargeAdvice;
                }
                // 转科
                if (type === AdviceRuleType.TRANSFER_DEPARTMENT) {
                    return TransferAdvice;
                }
                // 会诊
                if (type === AdviceRuleType.CONSULTATION) {
                    return ConsultationAdvice;
                }
                // 护理治疗
                if (
                    type === AdviceRuleType.TREATMENT ||
                    type === AdviceRuleType.NURSE ||
                    type === AdviceRuleType.NURSE_LEVEL) {
                    return TreatmentAdvice;
                }
                // 手术、术后
                if (type === AdviceRuleType.SURGERY || type === AdviceRuleType.POSTOPERATIVE) {
                    return SurgeryAdvice;
                }
                // 物资
                if (
                    type === AdviceRuleType.MEDICINE_MATERIAL ||
                    type === AdviceRuleType.SELF_PRODUCT ||
                    type === AdviceRuleType.HEALTH_MEDICINE ||
                    type === AdviceRuleType.HEALTH_FOOD ||
                    type === AdviceRuleType.MEDICINE_MATERIAL ||
                    type === AdviceRuleType.OTHER_PRODUCT
                ) {
                    return MaterialAdvice;
                }
            },
            handeClearSurgeryApplyList() {
                this.$emit('handeClearSurgeryApplyList');
            },
        },
    };
</script>
<style lang="scss">
.edit-medical-advice-group-wrapper {
    position: relative;

    .group-line {
        position: absolute;
        top: 20px;
        right: 1120px;
        width: 7px;
        border-top: 2px solid #8f8f8f;
        border-right: 2px solid #8f8f8f;
        border-bottom: 2px solid #8f8f8f;
    }
}
</style>
