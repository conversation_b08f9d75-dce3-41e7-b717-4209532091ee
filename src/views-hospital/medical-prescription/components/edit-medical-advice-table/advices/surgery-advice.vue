<template>
    <div class="edit-medical-advice-item tr">
        <div class="td table-sticky" :class="`width-${MPAddTable.checkbox.width}`">
            <div class="td-cell">
                <abc-checkbox v-model="advice.isChecked">
                </abc-checkbox>
            </div>
        </div>
        <div class="td table-sticky" :class="`width-${MPAddTable.type.width}`" :style="{ left: `${MPAddTable.checkbox.width}px` }">
            <abc-select
                v-model="curType"
                :disabled="isSurgery"
                no-icon
                :width="MPAddTable.type.width"
                :inner-width="100"
            >
                <abc-option
                    v-for="item in typeList"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                >
                </abc-option>
            </abc-select>
        </div>
        <advice-name
            :advice="advice"
            :advice-type="curType"
            :patient="patient"
            :json-type-with-custom-type-list="jsonTypeWithCustomTypeList"
            @select="handleChangeItem"
        ></advice-name>

        <div
            v-if="isSurgery"
            class="td"
            :class="`width-${MPAddTable.medicalDetail.width}`"
            :style="MPAddTable.medicalDetail.style"
            @click="isOpenSurgeryDialog"
        >
            <abc-form-item required>
                <abc-input
                    v-model="surgeryInfo"
                    :width="322"
                    readonly
                    placeholder="填写手术单"
                ></abc-input>
            </abc-form-item>
        </div>
        <div
            v-else
            class="td is-cell-disabled"
            :class="`width-${MPAddTable.medicalDetail.width}`"
        ></div>

        <div class="td" :class="`width-${MPAddTable.remark.width}`">
            <advice-remark
                v-model="advice.remark"
                placeholder="备注"
                :max-length="50"
                :remark-type="AdviceRemarkTypeEnum.Surgery"
                :tag-types="groupItem.tagTypes"
                :advice-tag-types="tagTypes"
                @change-tags="handleChangeTags"
            ></advice-remark>
        </div>
        <div class="td" :class="`width-${MPAddTable.dosageCount.width}`">
            <abc-form-item required>
                <abc-input
                    v-abc-focus-selected
                    :value="advice.dosageCount"
                    :width="82"
                    :input-custom-style="{ padding: '3px' }"
                    margin="0"
                    :max-length="5"
                    :config="{
                        max: 99999, formatLength: 1,supportZero: false,
                    }"
                    type="number"
                    class="count-center"
                    @enter="enterEvent"
                    @input="handleDosageCountInput"
                >
                </abc-input>
            </abc-form-item>
            <div class="input-append-unit">
                {{ advice.items[0].packageUnit }}
            </div>
        </div>
        <div class="td" :class="`width-${MPAddTable.executeDepartment.width}`">
            <abc-select
                :show-value="isSurgery ? executeDepartmentName : curPatientHospitalInfo.wardName"
                :width="MPAddTable.executeDepartment.width"
                no-icon
                disabled
            ></abc-select>
        </div>
        <div class="td" :class="`width-${MPAddTable.startTime.width}`">
            <start-time
                v-model="curStartTime"
                :advice-rule-type="advice.type"
                :tag-types="groupItem.tagTypes"
                supplement-label="手术时间为过去时间"
                @change="handleChangeSurgeryDate"
            ></start-time>
        </div>
        <div class="td is-cell-disabled" :class="`width-${MPAddTable.stopTime.width}`">
        </div>
        <div class="td is-cell-disabled" :class="`width-${MPAddTable.executeTime.width}`">
        </div>
        <div class="td" :class="`width-${MPAddTable.products.width}`" :style="MPAddTable.products.style">
            <relate-product
                :advice="advice"
                :advice-type="curType"
                is-related
                @change-pay-type="val => $set(advice, 'goodsIdMedicalInsurancePayTypes', val)"
            ></relate-product>
        </div>
    </div>
</template>

<script>
    import StartTime from '@/views-hospital/medical-prescription/components/advice-time/start-time.vue';
    import AdviceName
        from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/advices/advice-name-td/advice-name.vue';
    import RelateProduct
        from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/advices/related-product/relate-product.vue';
    import AdviceRemark from '@/views-hospital/medical-prescription/components/advice-remark/index.vue';

    import {
        AdviceRemarkTypeEnum,
        AdviceRuleType,
        AdviceTagEnum,
        AdviceLevelTags,
        MedicalAdviceTypeEnum,
        MedicalAdviceTypeStr,
    } from '@/views-hospital/medical-prescription/utils/constants.js';
    import { MPAddTable } from '@/views-hospital/medical-prescription/model/mp-add-table.js';
    import { MedicalPrescriptionService } from '@/views-hospital/medical-prescription/model/index.js';
    import { GoodsTypeIdEnum } from '@abc/constants';
    import { mapGetters } from 'vuex';
    import {
        SupplementAdviceConfirm,
    } from '@/views-hospital/medical-prescription/components/supplement-advice-confirm/supplement-advice-confirm.js';
    import { parseTime } from 'utils/index.js';
    import SurgeryApplyDialog
        from '@/views-hospital/medical-prescription/components/surgery/components/surgery-apply-dialog';
    import clone from 'utils/clone';
    import { getIsBeforeToday } from '@/views-hospital/medical-prescription/utils/common';

    export default {
        name: 'SurgeryAdvice',
        components: {
            StartTime,
            AdviceName,
            RelateProduct,
            AdviceRemark,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            advice: {
                type: Object,
            },
            deviceType: {
                type: [String, Number],
            },
            extendSpec: {
                type: [String, Number],
            },
            startTime: String,
            stopTime: String,
            type: [String, Number],
            endTime: String,
            disabled: Boolean,
            patientOrderId: {
                type: String,
                default: '',
            },
            patient: {
                type: Object,
                required: true,
            },
            surgeryReqs: {
                type: Array,
                default: () => ([]),
            },
            no: {
                type: [Number, String],
                default: 0,
            },
            doctor: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            departmentName: {
                type: String,
                default: '',
            },
            departments: {
                type: Array,
                default: () => ([]),
            },
            groupItem: Object,
            operateDepartmentId: String,
            tagTypes: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                MPAddTable,
                AdviceRemarkTypeEnum,
                typeList: [
                    {
                        value: MedicalAdviceTypeEnum.ONE_TIME,
                        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.ONE_TIME],
                    },
                    {
                        value: MedicalAdviceTypeEnum.LONG_TIME,
                        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.LONG_TIME],
                    },
                ],
                surgeryApplyRelationalInfo: {
                    isRelational: 0, // 是否关联手术单
                    relationalSurgeryApply: '', // 关联手术单id
                },
                cacheSurgeryApplyRelationalInfo: {
                    isRelational: 0, // 是否关联手术单
                    relationalSurgeryApply: '', // 关联手术单id
                },
            };
        },
        computed: {
            ...mapGetters(['currentClinic']),
            curPatientHospitalInfo() {
                return this.$abcPage.$store.curPatientHospitalInfo || {};
            },
            jsonTypeWithCustomTypeList() {
                return [
                    {
                        typeId: GoodsTypeIdEnum.SURGERY_SURGERY,
                    },
                    {
                        typeId: GoodsTypeIdEnum.SURGERY_AFTER_SURGERY,
                    },
                ];
            },
            groupItemHandle: {
                set(val) {
                    this.$emit('update:groupItem', val);
                },
                get() {
                    return this.groupItem;
                },
            },
            curType: {
                get() {
                    return this.type;
                },
                set(v) {
                    this.$emit('update:type', v);
                },
            },
            curStartTime: {
                get() {
                    return this.startTime;
                },
                set(v) {
                    this.$emit('update:startTime', v);
                },
            },
            curStopTime: {
                get() {
                    return this.stopTime;
                },
                set(v) {
                    this.$emit('update:stopTime', v);
                },
            },
            surgeryApplySheetReqsItem() {
                return this.surgeryReqs.find((it) => this.advice.applySheetSourceId === it.sourceId);
            },
            surgeryItem() {
                return this.surgeryApplySheetReqsItem || {};
            },
            surgeryInfo() {
                return this.surgeryItem.surgeryArrangement?.surgeryDate ? `拟于：${this.surgeryItem.surgeryArrangement.surgeryDate} 开始` : '';
            },
            patientInfo() {
                return this.patient;
            },
            // 执行科室名称
            executeDepartmentName() {
                const { surgeryDepartmentId = '' } = this.surgeryApplySheetReqsItem || {};
                const surgeryDepartment = this.departments.find((it) => it.id === surgeryDepartmentId);
                return surgeryDepartment?.name || '';
            },
            // 手术医嘱
            isSurgery() {
                return this.advice.type === AdviceRuleType.SURGERY;
            },
            // 术后医嘱
            isSurgeryAfter() {
                return this.advice.type === AdviceRuleType.POSTOPERATIVE;
            },
        },
        mounted() {
            this.$nextTick(() => {
                if (this.isSurgery) {
                    this.isOpenSurgeryDialog();
                } else {
                    this.message = this.$confirm({
                        type: 'warn',
                        title: '是否自动停长嘱',
                        confirmText: '确定',
                        cancelText: '不停止',
                        content: `确定后，所有长期医嘱将于${this.curStartTime}自动停止。`,
                        onConfirm: () => {
                            this.advice.isStopBeforeStartLongAdvice = 1;
                        },
                        onCancel: () => {
                            this.advice.isStopBeforeStartLongAdvice = 0;
                        },
                    });
                }
            });
        },
        methods: {
            handleChangeTags(tags, isNeedConfirm = false) {
                if (tags.includes(AdviceTagEnum.SUPPLEMENT) && isNeedConfirm) {
                    new SupplementAdviceConfirm({
                        startTime: this.curStartTime,
                        stopTime: this.curStopTime,
                        adviceRemark: this.advice.remark,
                        adviceType: this.curType,
                        adviceRuleType: this.advice.type,
                        timeLabel: '手术日期',
                        onConfirm: ({
                            startTime, stopTime, remark,
                        }) => {
                            this.curStartTime = startTime;
                            this.curStopTime = stopTime;
                            this.advice.remark = remark;

                            this.handleChangeSurgeryDate(startTime);
                        },
                        onCancel: () => {
                            this.groupItem.tagTypes = this.groupItem.tagTypes.filter((item) => item !== AdviceTagEnum.SUPPLEMENT);
                        },
                    }).generateDialog({
                        parent: this,
                    });
                }
                if (this.groupItem.tagTypes.includes(AdviceTagEnum.SUPPLEMENT) && !tags.includes(AdviceTagEnum.SUPPLEMENT)) {
                    this.curStopTime = '';
                    const startTime = parseTime(new Date(), 'y-m-d h:i', true);
                    this.curStartTime = startTime;
                    this.handleChangeSurgeryDate(startTime);
                }

                // 分离组级别和医嘱级别的tag
                const groupLevelTags = tags.filter((tag) => !AdviceLevelTags.includes(tag));
                const adviceLevelTags = tags.filter((tag) => AdviceLevelTags.includes(tag));

                // 更新组级别的tag
                this.groupItem.tagTypes = groupLevelTags;

                // 更新当前医嘱的tag
                this.$emit('update:tagTypes', adviceLevelTags);
            },
            enterEvent(e) {
                this.$emit('enter', e);
            },
            isOpenSurgeryDialog() {
                this.cacheSurgeryApplyRelationalInfo = clone(this.surgeryApplyRelationalInfo);
                // 打开手术申请单
                new SurgeryApplyDialog({
                    surgeryReqListProp: this.surgeryReqs || [],
                    surgeryReqProp: this.surgeryItem || {},
                    doctor: this.doctor || {},
                    patientOrderId: this.patientOrderId || '',
                    goodsInfo: this.advice.items?.[0] || {},
                    cacheSurgeryApplyRelationalInfo: this.cacheSurgeryApplyRelationalInfo,
                    advice: this.advice,
                    confirm: this.handleFinishSurgeryApply,
                    pushCb: this.handlePush,
                    spliceCb: this.handleSplice,
                    handleChangeStartTimeBySurgeryData: this.handleChangeStartTimeBySurgeryData,
                    verifySurgeryDate: this.verifySurgeryDate,
                    startTime: this.curStartTime,
                }).generateDialogAsync({ parent: this });
            },
            async handleChangeItem(goods) {
                const adviceItem = MedicalPrescriptionService.getAdviceGoodsItem(goods);
                this.advice.items = [adviceItem];
                this.advice.type = MedicalPrescriptionService.getAdviceRuleType(goods);
                // 初始化医嘱的数据
                this.advice.singleDosageCount = 1;
                this.advice.singleDosageUnit = goods.packageUnit;
                this.advice.dosageCount = this.advice.singleDosageCount;
                this.advice.dosageUnit = this.advice.singleDosageUnit;
                this.advice.isDismounting = 0;
                this.advice.remark = '';
            },
            handleDosageCountInput(v) {
                this.$set(this.advice, 'dosageCount', v);
                this.$set(this.advice, 'singleDosageCount', v);
            },
            handleFinishSurgeryApply(sourceId) {
                this.surgeryApplyRelationalInfo = clone(this.cacheSurgeryApplyRelationalInfo);
                this.advice.applySheetSourceId = sourceId;
                this.$emit('handeClearSurgeryApplyList');
            },
            handlePush(surgeryReq) {
                this.surgeryReqs.push(surgeryReq);
            },
            handleSplice(start, deleteCount, surgeryReq) {
                this.surgeryReqs.splice(start, deleteCount, surgeryReq);
            },
            handleChangeStartTimeBySurgeryData(surgeryData) {
                this.$nextTick(() => {
                    if (surgeryData) {
                        this.curStartTime = parseTime(new Date(surgeryData), 'y-m-d h:i', true);
                    }
                });
            },
            // 开始时间同步到手术单中的手术日期
            handleChangeSurgeryDate(v) {
                const findSurgeryReq = this.surgeryReqs.find((it) => it.sourceId === this.advice.applySheetSourceId);
                if (findSurgeryReq) {
                    findSurgeryReq.surgeryArrangement.surgeryDate = parseTime(new Date(v), 'y-m-d', true);
                }
            },
            verifySurgeryDate(surgeryData) {
                return new Promise((resolve, reject) => {
                    const hasSupplementAdvice = this.groupItem.tagTypes?.includes?.(AdviceTagEnum.SUPPLEMENT);
                    if (getIsBeforeToday(surgeryData) && !hasSupplementAdvice) {
                        this.$confirm({
                            type: 'info',
                            preset: 'message',
                            title: '手术日期为过去时间',
                            content: '确定后将补出一条过去开始的手术医嘱',
                            showIcon: true,
                            confirmText: '确定',
                            cancelText: '取消',
                            appendToBody: true,
                            onConfirm: () => {
                                this.groupItem.tagTypes?.push?.(AdviceTagEnum.SUPPLEMENT);
                                resolve();
                            },
                            onCancel: () => {
                                reject();
                            },
                        });
                        return;
                    }
                    if (!getIsBeforeToday(surgeryData) && hasSupplementAdvice) {
                        const findIndex = this.groupItem.tagTypes?.indexOf?.(AdviceTagEnum.SUPPLEMENT);
                        if (findIndex > -1) {
                            this.groupItem.tagTypes?.splice?.(findIndex, 1);
                        }
                    }
                    resolve();
                });
            },
        },
    };
</script>
<style lang="scss">
@import "src/styles/theme.scss";
@import 'src/styles/mixin.scss';

.edit-medical-advice-item_exam--choice {
    color: $T2;
    cursor: pointer;
}
</style>

