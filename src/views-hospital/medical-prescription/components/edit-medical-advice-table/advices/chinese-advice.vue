<template>
    <div class="edit-medical-advice-item tr">
        <div class="td table-sticky" :class="`width-${MPAddTable.checkbox.width}`">
            <div class="td-cell">
                <abc-checkbox v-model="advice.isChecked">
                </abc-checkbox>
            </div>
        </div>
        <div class="td table-sticky" :class="`width-${MPAddTable.type.width}`" :style="{ left: `${MPAddTable.checkbox.width}px` }">
            <abc-select
                v-model="curType"
                no-icon
                :width="48"
                :inner-width="100"
            >
                <abc-option
                    v-for="item in typeList"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                >
                </abc-option>
            </abc-select>
        </div>
        <div
            class="td table-sticky"
            :class="`width-${MPAddTable.name.width}`"
            :style="[MPAddTable.name.style, { left: `${MPAddTable.checkbox.width + MPAddTable.type.width}px` }]"
            style="cursor: pointer;"
            @click.stop="handleOpenDialog"
        >
            <div class="ellipsis" :style="MPAddTable.name.contentStyle">
                <span>{{ adviceName }}</span>
                <abc-tips
                    v-if="advice.passAuditStatus"
                    icon
                    size="small"
                    theme="warning"
                >
                    有用药风险
                </abc-tips>
            </div>
        </div>
        <div
            class="td edit-medical-advice-item_exam--choice"
            style="padding: 0 8px; cursor: default;"
            :class="`width-${MPAddTable.medicalDetail.width}`"
            :style="MPAddTable.medicalDetail.style"
        >
            <span>{{ chineseUsageStr }}</span>
        </div>
        <div
            class="td"
            :class="`width-${MPAddTable.remark.width}`"
            style="padding-left: 8px;"
        >
            <div style="width: 42px; min-width: 42px; max-width: 42px;" class="ellipsis" :title="chineseRemark">
                {{ chineseRemark }}
            </div>
            <goods-remark
                ref="medicineRemarks"
                v-model="advice.remark"
                placeholder="备注"
                max-length="200"
                placement="bottom-start"
                :readonly="false"
                support-tag
                :tabindex="-1"
                :tag-types="groupItem.tagTypes"
                :advice-tag-types="tagTypes"
                show-medicine-remark-options
                :show-medicine-source-options="false"
                :show-psychotropic-narcotic-type="true"
                show-chinese-remark-options
                @change-tags="handleChangeTags"
                @enter="enterEvent"
            ></goods-remark>
        </div>
        <div class="td is-cell-disabled" :class="`width-${MPAddTable.dosageCount.width}`">
        </div>
        <div class="td" :class="`width-${MPAddTable.executeDepartment.width}`">
            <abc-select
                :show-value="pharmacyName"
                :width="MPAddTable.executeDepartment.width"
                no-icon
                disabled
            ></abc-select>
        </div>
        <div class="td" :class="`width-${MPAddTable.startTime.width}`">
            <start-time
                v-model="curStartTime"
                :tag-types="groupItem.tagTypes"
                :advice-rule-type="advice.type"
                @enter="enterEvent"
                @change="handleChangeStartTime"
            ></start-time>
        </div>
        <div class="td" :class="[`width-${MPAddTable.stopTime.width}`, !!curStopTime ? 'is-cell-disabled' : '']">
            <stop-time
                v-if="isLongTimeAdvice"
                v-model="curStopTime"
                :start-time="curStartTime"
                :disabled="!!curStopTime"
                @enter="enterEvent"
            ></stop-time>
        </div>
        <div class="td" :class="`width-${MPAddTable.executeTime.width} ${isLongTimeAdvice ? '' : 'is-cell-disabled'}`">
            <execute-time-select
                v-if="isLongTimeAdvice"
                :freq-info="curFreqInfo"
            ></execute-time-select>
        </div>
        <div
            class="td ellipsis"
            :class="`width-${MPAddTable.products.width}`"
            :style="MPAddTable.products.style"
        >
            <relate-product
                :advice-type="curType"
                :advice="advice"
                is-related
                is-medicine
                @change-pay-type="val => $set(advice, 'goodsIdMedicalInsurancePayTypes', val)"
            ></relate-product>
        </div>

        <chinese-medical-dialog
            v-if="isShowDialog"
            v-model="isShowDialog"
            :group-item="groupItem"
        ></chinese-medical-dialog>
    </div>
</template>

<script>
    import {
        MedicalAdviceTypeStr,
        MedicalAdviceTypeEnum,
        AdviceRuleType,
        AdviceTagEnum,
        AdviceLevelTags,
    } from '@/views-hospital/medical-prescription/utils/constants.js';

    import { MPAddTable } from '@/views-hospital/medical-prescription/model/mp-add-table.js';

    import ExecuteTimeSelect from '@/views-hospital/medical-prescription/components/execute-time-select/index.vue';
    import ChineseMedicalDialog
        from '@/views-hospital/medical-prescription/components/chinese-medical/chinese-medical-dialog.vue';
    import StartTime from '@/views-hospital/medical-prescription/components/advice-time/start-time.vue';
    import StopTime from '@/views-hospital/medical-prescription/components/advice-time/stop-time.vue';
    import GoodsRemark from '@/views-hospital/medical-prescription/components/goods-remark/index.vue';

    import { mapGetters } from 'vuex';


    import { calcChineseStopTime } from '@/views-hospital/medical-prescription/utils/chinese-calcuelate.js';
    import {
        SupplementAdviceConfirm,
    } from '@/views-hospital/medical-prescription/components/supplement-advice-confirm/supplement-advice-confirm.js';
    import { parseTime } from 'utils/index.js';
    import { getFreqInfoByFirst } from '@/views-hospital/medical-prescription/utils/format-advice';
    import RelateProduct
        from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/advices/related-product/relate-product.vue';

    export default {
        name: 'ChineseAdvice',
        components: {
            RelateProduct,
            ExecuteTimeSelect,
            ChineseMedicalDialog,
            StopTime,
            StartTime,
            GoodsRemark,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            advice: {
                type: Object,
            },
            startTime: String,
            type: [String, Number],
            stopTime: String,
            disabled: Boolean,
            patientOrderId: {
                type: String,
                default: '',
            },
            freqInfo: [Object, null],
            freq: String,
            groupItem: Object,
            tagTypes: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                MPAddTable,
                MedicalAdviceTypeEnum,
                typeList: [
                    {
                        value: MedicalAdviceTypeEnum.LONG_TIME,
                        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.LONG_TIME],
                    },
                    {
                        value: MedicalAdviceTypeEnum.ONE_TIME,
                        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.ONE_TIME],
                    },
                    {
                        value: MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE,
                        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE],
                    },
                ],
                isShowDialog: false,
            };
        },
        computed: {
            ...mapGetters('executeTime', ['allExecuteTimeList']),
            ...mapGetters(['enableLocalPharmacyList']),
            pharmacyName() {
                const { pharmacyNo } = this.advice;
                const pharmacyItem = this.enableLocalPharmacyList.find((it) => it.no === pharmacyNo);
                return pharmacyItem && pharmacyItem.name ? pharmacyItem.name : '自备';
            },
            curStartTime: {
                get() {
                    return this.startTime;
                },
                set(v) {
                    this.$emit('update:startTime', v);
                },
            },
            curStopTime: {
                get() {
                    return this.stopTime;
                },
                set(v) {
                    this.$emit('update:stopTime', v);
                },
            },
            curFreqInfo: {
                get() {
                    return this.freqInfo;
                },
                set(v) {
                    this.$emit('update:freqInfo', v);
                },
            },
            curFreq: {
                get() {
                    return this.freq;
                },
                set(v) {
                    this.$emit('update:freq', v);
                },
            },
            curType: {
                get() {
                    return this.type;
                },
                set(v) {
                    this.$emit('update:type', v);
                },
            },
            isLongTimeAdvice() {
                return this.curType === MedicalAdviceTypeEnum.LONG_TIME;
            },
            productDetail() {
                const type = this.advice.type === AdviceRuleType.CHINESE_MEDICINE_GRANULES ? '中药颗粒' : '中药饮片';
                const count = this.advice.isNeedProcess ? 2 : 1;
                return `${count}项：${type} ${this.advice.isNeedProcess ? '，加工费' : ''}`;
            },
            adviceName() {
                const type = this.advice.type === AdviceRuleType.CHINESE_MEDICINE_GRANULES ? '中药颗粒' : '中药饮片';
                const { processInfo } = this.advice;
                let processInfoStr = '';
                if (processInfo) {
                    const {
                        displayName,
                        perDosageBagCount,
                    } = processInfo;

                    processInfoStr = displayName || '';
                    if (perDosageBagCount) {
                        processInfoStr += `-1剂煎${perDosageBagCount}袋`;
                    }
                    if (processInfoStr) {
                        processInfoStr = `[${processInfoStr}]`;
                    }
                }
                return `${type} 共${this.advice.dosageCount}剂 ${processInfoStr}`;
            },
            chineseUsageStr() {
                return `${this.groupItem.usage},${this.advice.dailyDosage},${this.groupItem.freq},${this.advice.singleDosageCount}`;
            },
            chineseRemark() {
                let remarkStr = [];
                this.advice.items.forEach((item) => {
                    remarkStr.push(`${item.medicineCadn} ${item?.remark || ''}`) ;
                });
                remarkStr = remarkStr.join('，');
                return remarkStr;
            },
            executeTimeList() {
                return this.allExecuteTimeList || [];
            },
        },
        watch: {
            curType: {
                handler(val) {
                    if (val === MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE) {
                        this.curStopTime = '';
                    }
                },
            },
            freqInfo: {
                handler() {
                    this.curStopTime = calcChineseStopTime(this.groupItem);
                },
                deep: true,
            },
        },
        methods: {
            enterEvent(e) {
                this.$emit('enter', e);
            },
            handleOpenDialog() {
                this.isShowDialog = true;
            },
            handleChangeStartTime(val) {
                this.curStopTime = calcChineseStopTime(this.groupItem);
                this.curFreqInfo = getFreqInfoByFirst(this.executeTimeList, this.curFreq, true, val);
            },
            handleChangeTags(tags, isNeedConfirm = false) {
                if (tags.includes(AdviceTagEnum.SUPPLEMENT) && isNeedConfirm) {
                    new SupplementAdviceConfirm({
                        startTime: this.curStartTime,
                        stopTime: this.curStopTime,
                        adviceRemark: this.advice.remark,
                        adviceType: this.curType,
                        adviceRuleType: this.advice.type,
                        maxLength: 200,
                        onConfirm: ({
                            startTime,
                            remark,
                        }) => {
                            this.curStartTime = startTime;
                            this.curStopTime = calcChineseStopTime(this.groupItem);
                            this.advice.remark = remark;
                        },
                        onCancel: () => {
                            this.groupItem.tagTypes = this.groupItem.tagTypes.filter((item) => item !== AdviceTagEnum.SUPPLEMENT);
                        },
                    }).generateDialog({
                        parent: this,
                    });
                }
                if (this.groupItem.tagTypes.includes(AdviceTagEnum.SUPPLEMENT) && !tags.includes(AdviceTagEnum.SUPPLEMENT)) {
                    this.curStopTime = '';
                    this.curStartTime = parseTime(new Date(), 'y-m-d h:i', true);
                }

                // 分离组级别和医嘱级别的tag
                const groupLevelTags = tags.filter((tag) => !AdviceLevelTags.includes(tag));
                const adviceLevelTags = tags.filter((tag) => AdviceLevelTags.includes(tag));

                // 更新组级别的tag
                this.groupItem.tagTypes = groupLevelTags;

                // 更新当前医嘱的tag
                this.$emit('update:tagTypes', adviceLevelTags);
            },
        },
    };
</script>
<style lang="scss">
@import "src/styles/theme.scss";
@import 'src/styles/mixin.scss';

.edit-medical-advice-item_exam--choice {
    color: $T2;
}
</style>

