<template>
    <div class="edit-medical-advice-item tr">
        <div class="td table-sticky" :class="`width-${MPAddTable.checkbox.width}`">
            <div class="td-cell">
                <abc-checkbox v-model="advice.isChecked">
                </abc-checkbox>
            </div>
        </div>
        <div class="td table-sticky" :class="`width-${MPAddTable.type.width}`" :style="{ left: `${MPAddTable.checkbox.width}px` }">
            <abc-select
                v-model="dischargeType"
                disabled
                no-icon
                :width="48"
                :inner-width="100"
            >
                <abc-option
                    v-for="item in typeList"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                >
                </abc-option>
            </abc-select>
        </div>
        <div
            class="td table-sticky"
            :class="`width-${MPAddTable.name.width}`"
            :style="[MPAddTable.name.style, { left: `${MPAddTable.checkbox.width + MPAddTable.type.width}px` }]"
            :title="adviceName"
        >
            <div class="ellipsis" :style="MPAddTable.name.contentStyle">
                <span>{{ adviceName }}</span>
            </div>
        </div>
        <div
            class="td"
            :class="`width-${MPAddTable.medicalDetail.width}`"
            :style="MPAddTable.medicalDetail.style"
            @click="isTransferDepartmentDialog = true"
        >
            <abc-form-item required>
                <abc-input
                    v-model="transferDepartmentStr"
                    readonly
                    :width="322"
                    placeholder="确认转入科室"
                    @enter="enterEvent"
                ></abc-input>
            </abc-form-item>
        </div>
        <div class="td" :class="`width-${MPAddTable.remark.width}`">
            <advice-remark
                v-model="advice.remark"
                placeholder="备注"
                :max-length="50"
                :remark-type="AdviceRemarkTypeEnum.Discharge"
                :tag-types="groupItem.tagTypes"
                :advice-tag-types="tagTypes"
                @change-tags="handleChangeTags"
            ></advice-remark>
        </div>
        <div class="td is-cell-disabled" :class="`width-${MPAddTable.dosageCount.width}`"></div>
        <div class="td" :class="`width-${MPAddTable.executeDepartment.width}`">
            <abc-select
                :show-value="curPatientHospitalInfo.wardName"
                :width="MPAddTable.executeDepartment.width"
                no-icon
                disabled
            ></abc-select>
        </div>
        <div class="td" :class="`width-${MPAddTable.startTime.width}`">
            <start-time
                v-model="curStartTime"
                :tag-types="groupItem.tagTypes"
                :advice-rule-type="advice.type"
                @enter="enterEvent"
            ></start-time>
        </div>
        <div class="td is-cell-disabled" :class="`width-${MPAddTable.stopTime.width}`"></div>
        <div class="td is-cell-disabled" :class="`width-${MPAddTable.executeTime.width}`">
        </div>
        <div class="td" :class="`width-${MPAddTable.products.width}`" :style="MPAddTable.products.style">
        </div>
        <transfer-department-dialog
            v-if="isTransferDepartmentDialog"
            v-model="isTransferDepartmentDialog"
            :transfer-to-department="adviceCurrent.transferToDepartment"
            :current-departments="departments"
            :remark="adviceCurrent.remark"
            @transfer="transfer"
        ></transfer-department-dialog>
    </div>
</template>

<script>
    import {
        AdviceRemarkTypeEnum,
        AdviceTagEnum,
        AdviceLevelTags,
        MedicalAdviceTypeEnum,
        MedicalAdviceTypeStr,
        medicalPrescriptionOptionsEnum,
    } from '@/views-hospital/medical-prescription/utils/constants';
    import TransferDepartmentDialog from '../../transfer/transfer-department-dialog';
    import StartTime from '@/views-hospital/medical-prescription/components/advice-time/start-time.vue';
    import AdviceRemark from '@/views-hospital/medical-prescription/components/advice-remark/index.vue';

    import SettingAPI from 'api/settings';
    import { MPAddTable } from '@/views-hospital/medical-prescription/model/mp-add-table.js';
    import {
        SupplementAdviceConfirm,
    } from '@/views-hospital/medical-prescription/components/supplement-advice-confirm/supplement-advice-confirm.js';
    import { parseTime } from 'utils/index.js';


    export default {
        name: 'TransferAdvice',
        components: {
            TransferDepartmentDialog,
            StartTime,
            AdviceRemark,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            advice: {
                type: Object,
            },
            startTime: String,
            type: [String, Number],
            stopTime: String,
            disabled: Boolean,
            groupItem: Object,
        },
        data() {
            return {
                AdviceRemarkTypeEnum,
                MPAddTable,
                medicalPrescriptionOptionsEnum,
                typeList: [
                    {
                        value: MedicalAdviceTypeEnum.ONE_TIME,
                        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.ONE_TIME],
                    },
                ],
                transferDepartment: '',
                isTransferDepartmentDialog: false,
                departments: [],
            };
        },
        computed: {
            curPatientHospitalInfo() {
                return this.$abcPage.$store.curPatientHospitalInfo || {};
            },
            curType: {
                get() {
                    return this.type;
                },
                set(v) {
                    this.$emit('update:type', v);
                },
            },
            adviceName() {
                return this.adviceCurrent?.name;
            },
            dischargeType: {
                get() {
                    return this.type;
                },
                set(v) {
                    this.$emit('update:type', v);
                },
            },
            adviceCurrent: {
                get() {
                    return this.advice;
                },
                set(v) {
                    this.$emit('update:advice', v);
                },
            },
            curStartTime: {
                get() {
                    return this.startTime;
                },
                set(v) {
                    this.$emit('update:startTime', v);
                },
            },
            curStopTime: {
                get() {
                    return this.stopTime;
                },
                set(v) {
                    this.$emit('update:stopTime', v);
                },
            },
            transferDepartmentStr() {
                if (!this.transferToDepartmentName) {
                    return '';
                }
                return `转入: ${this.transferToDepartmentName}`;
            },
            transferToDepartmentName() {
                if (!this.departments?.length) {
                    return '';
                }
                const departmentsName = this.departments?.find((item) => {
                    return item.id === this.adviceCurrent.transferToDepartment;
                })?.name || '';
                return departmentsName;
            },
        },
        watch: {
            curStartTime(v) {
                this.adviceCurrent.stopLongAdviceTime = v || '';
            },
        },
        async created() {
            await this.fetchDepartments();
            this.isTransferDepartmentDialog = true;
        },
        methods: {
            enterEvent(e) {
                this.$emit('enter', e);
                this.$nextTick(() => {
                    $('.advice-product-autocomplete .abc-input__inner')?.eq(0)?.focus();
                });
            },
            async fetchDepartments() {
                try {
                    const res = await SettingAPI.clinic.fetchClinicDepartments();
                    const { data } = res.data;
                    this.departments = data?.rows || [];
                } catch (e) {
                    console.log('科室信息获取错误=', e);
                }
            },
            transfer(item) {
                const {
                    transferToDepartment, remark,
                } = item;
                const params = {
                    transferToDepartment,
                    remark,
                };
                this.adviceCurrent.stopLongAdviceTime = this.curStartTime;
                this.adviceCurrent = Object.assign(this.adviceCurrent, params);
            },
            handleChangeTags(tags, isNeedConfirm = false) {
                if (tags.includes(AdviceTagEnum.SUPPLEMENT) && isNeedConfirm) {
                    new SupplementAdviceConfirm({
                        startTime: this.curStartTime,
                        stopTime: this.curStopTime,
                        adviceRemark: this.advice.remark,
                        adviceType: this.curType,
                        adviceRuleType: this.advice.type,
                        onConfirm: ({
                            startTime, stopTime, remark,
                        }) => {
                            this.curStartTime = startTime;
                            this.curStopTime = stopTime;
                            this.advice.remark = remark;
                        },
                        onCancel: () => {
                            this.groupItem.tagTypes = this.groupItem.tagTypes.filter((item) => item !== AdviceTagEnum.SUPPLEMENT);
                        },
                    }).generateDialog({
                        parent: this,
                    });
                }
                if (this.groupItem.tagTypes.includes(AdviceTagEnum.SUPPLEMENT) && !tags.includes(AdviceTagEnum.SUPPLEMENT)) {
                    this.curStopTime = '';
                    this.curStartTime = parseTime(new Date(), 'y-m-d h:i', true);
                }

                // 分离组级别和医嘱级别的tag
                const groupLevelTags = tags.filter((tag) => !AdviceLevelTags.includes(tag));
                const adviceLevelTags = tags.filter((tag) => AdviceLevelTags.includes(tag));

                // 更新组级别的tag
                this.groupItem.tagTypes = groupLevelTags;

                // 更新当前医嘱的tag
                if (!this.advice.tagTypes) {
                    this.$set(this.advice, 'tagTypes', []);
                }
                this.advice.tagTypes = adviceLevelTags;
            },
        },
    };
</script>

