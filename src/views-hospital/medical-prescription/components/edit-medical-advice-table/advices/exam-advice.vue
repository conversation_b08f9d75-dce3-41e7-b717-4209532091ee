<template>
    <div class="edit-medical-advice-item tr">
        <div class="td table-sticky" :class="`width-${MPAddTable.checkbox.width}`">
            <div class="td-cell">
                <abc-checkbox v-model="advice.isChecked">
                </abc-checkbox>
            </div>
        </div>
        <div class="td table-sticky" :class="`width-${MPAddTable.type.width}`" :style="{ left: `${MPAddTable.checkbox.width}px` }">
            <abc-select
                v-model="type"
                disabled
                no-icon
                :width="48"
                :inner-width="100"
            >
                <abc-option
                    v-for="item in typeList"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                >
                </abc-option>
            </abc-select>
        </div>
        <advice-name
            :advice="advice"
            :advice-type="type"
            :patient="patient"
            :json-type-with-custom-type-list="jsonTypeWithCustomTypeList"
            @select="handleChangeItem"
        ></advice-name>

        <div
            class="td edit-medical-advice-item_exam--choice"
            :class="`width-${MPAddTable.medicalDetail.width}`"
            :style="MPAddTable.medicalDetail.style"
            @click="isOpenExamDialog"
        >
            <abc-form-item>
                <abc-input
                    v-model="examInfo"
                    :width="322"
                    readonly
                    :disabled="isNeedOpenAdviceSupplement"
                    :placeholder="placeholderStr"
                ></abc-input>
            </abc-form-item>
        </div>
        <div class="td" :class="`width-${MPAddTable.remark.width}`">
            <advice-remark
                v-model="advice.remark"
                placeholder="备注"
                :max-length="50"
                :remark-type="isInspect ? AdviceRemarkTypeEnum.Inspect : AdviceRemarkTypeEnum.Examination"
                :tag-types="groupItem.tagTypes"
                :advice-tag-types="tagTypes"
                @change-tags="handleChangeTags"
            ></advice-remark>
        </div>
        <div class="td" :class="`width-${MPAddTable.dosageCount.width}`">
            <abc-form-item required>
                <abc-input
                    v-abc-focus-selected
                    :value="advice.dosageCount"
                    :width="82"
                    :input-custom-style="{ padding: '3px' }"
                    margin="0"
                    :max-length="5"
                    :config="{
                        min: 1, max: 99999, formatLength: 0
                    }"
                    type="number"
                    class="count-center"
                    @enter="enterEvent"
                    @input="handleDosageCountInput"
                >
                </abc-input>
            </abc-form-item>
            <div class="input-append-unit">
                {{ advice.items[0].packageUnit }}
            </div>
        </div>
        <div class="td" :class="`width-${MPAddTable.executeDepartment.width}`">
            <abc-select
                :show-value="executeDepartmentName"
                :width="MPAddTable.executeDepartment.width"
                no-icon
                disabled
            ></abc-select>
        </div>
        <div class="td" :class="`width-${MPAddTable.startTime.width}`">
            <start-time
                v-model="curStartTime"
                :advice-rule-type="advice.type"
                :tag-types="groupItem.tagTypes"
                @change="changeCurStartTime"
            ></start-time>
        </div>
        <div class="td is-cell-disabled" :class="`width-${MPAddTable.stopTime.width}`">
        </div>
        <div class="td is-cell-disabled" :class="`width-${MPAddTable.executeTime.width}`">
        </div>
        <div class="td" :class="`width-${MPAddTable.products.width}`" :style="MPAddTable.products.style">
            <relate-product
                :advice="advice"
                :advice-type="type"
                is-related
                @change-pay-type="val => $set(advice, 'goodsIdMedicalInsurancePayTypes', val)"
            ></relate-product>
        </div>
        <single-examination-medical-prescription-dialog
            v-if="isShowExaminationDialog"
            v-model="isShowExaminationDialog"
            :patient-order-id="patientOrderId"
            :patient="patient"
            :device-type="deviceType"
            :advice="advice"
            :extend-spec="extendSpec"
            :doctor="doctor"
            :no="no"
            :operate-department-id="operateDepartmentId"
            :cur-start-time="curStartTime"
            :exam-item.sync="examItem"
            :department-name="departmentName"
            :exam-advice-groups="examAdviceGroups"
            @createExamApplySheetId="createExamApplySheetId"
        ></single-examination-medical-prescription-dialog>
    </div>
</template>

<script>
    import SingleExaminationMedicalPrescriptionDialog from '@/views-hospital/medical-prescription/components/examination/single-examination-medical-prescription';
    import StartTime from '@/views-hospital/medical-prescription/components/advice-time/start-time.vue';
    import AdviceName
        from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/advices/advice-name-td/advice-name.vue';
    import RelateProduct
        from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/advices/related-product/relate-product.vue';
    import AdviceRemark from '@/views-hospital/medical-prescription/components/advice-remark/index.vue';

    import {
        MedicalAdviceTypeStr,
        MedicalAdviceTypeEnum,
        AdviceRuleType,
        AdviceRemarkTypeEnum,
        AdviceTagEnum,
        AdviceLevelTags,
    } from '@/views-hospital/medical-prescription/utils/constants.js';
    import { MPAddTable } from '@/views-hospital/medical-prescription/model/mp-add-table.js';
    import { MedicalPrescriptionService } from '@/views-hospital/medical-prescription/model/index.js';
    import { GoodsTypeIdEnum } from '@abc/constants';
    import { mapGetters } from 'vuex';
    import { formatDate } from '@abc/utils-date';
    import MedicalPrescriptionAPI from 'api/hospital/medical-prescription';
    import { SupplementAdviceConfirm } from '@/views-hospital/medical-prescription/components/supplement-advice-confirm/supplement-advice-confirm.js';
    import { AdviceExaminationExtendSpec } from '@/views-hospital/medical-prescription/utils/constants';
    import { parseTime } from 'utils/index.js';
    export default {
        name: 'ExamAdvice',
        components: {
            SingleExaminationMedicalPrescriptionDialog,
            StartTime,
            AdviceName,
            RelateProduct,
            AdviceRemark,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            advice: {
                type: Object,
            },
            deviceType: {
                type: [String, Number],
            },
            extendSpec: {
                type: [String, Number],
            },
            startTime: String,
            stopTime: String,
            type: [String, Number],
            endTime: String,
            disabled: Boolean,
            patientOrderId: {
                type: String,
                default: '',
            },
            patient: {
                type: Object,
                required: true,
            },
            examAdviceGroups: {
                type: Array,
            },
            examApplySheetReqs: {
                type: Array,
            },
            no: {
                type: [Number, String],
                default: 0,
            },
            doctor: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            departmentName: {
                type: String,
                default: '',
            },
            groupItem: Object,
            operateDepartmentId: String,
            tagTypes: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                MPAddTable,
                AdviceRemarkTypeEnum,
                typeList: [
                    {
                        value: MedicalAdviceTypeEnum.ONE_TIME,
                        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.ONE_TIME],
                    },
                    {
                        value: MedicalAdviceTypeEnum.LONG_TIME,
                        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.LONG_TIME],
                    },
                ],
                isShowExaminationDialog: false,
                isSearching: false,
                curKeyword: '',
            };
        },
        computed: {
            ...mapGetters(['currentClinic']),
            isNeedOpenAdviceSupplement() {
                return !!this.groupItemHandle.tagTypes?.filter((item) => {
                    return item === AdviceTagEnum.SUPPLEMENT;
                }).length;
            },
            jsonTypeWithCustomTypeList() {
                return [
                    {
                        typeId: GoodsTypeIdEnum.EXAMINATION_ASSAY,
                    },
                    {
                        typeId: GoodsTypeIdEnum.EXAMINATION_INSPECTION,
                    },
                ];
            },
            groupItemHandle: {
                set(val) {
                    this.$emit('update:groupItem', val);
                },
                get() {
                    return this.groupItem;
                },
            },
            curStartTime: {
                get() {
                    return this.startTime;
                },
                set(v) {
                    this.$emit('update:startTime', v);
                },
            },
            curStopTime: {
                get() {
                    return this.stopTime;
                },
                set(v) {
                    this.$emit('update:stopTime', v);
                },
            },
            // 检查检验申请单
            examApplySheetReqsItem() {
                return this.examApplySheetReqs?.find((it) => {
                    // 检查需要判断类型为检查和设备类型匹配
                    if (this.advice.type === AdviceRuleType.INSPECTION && this.extendSpec === AdviceExaminationExtendSpec.DEFAULT) {
                        return it.type === 2 && it.deviceType === this.deviceType && this.extendSpec === it.extendSpec && formatDate(it.planExecuteDate, 'YYYY-MM-DD') === formatDate(this.curStartTime, 'YYYY-MM-DD');
                    }
                    if (this.advice.type === AdviceRuleType.INSPECTION && this.extendSpec !== AdviceExaminationExtendSpec.DEFAULT) {
                        return it.type === 2 && this.extendSpec === it.extendSpec && formatDate(it.planExecuteDate, 'YYYY-MM-DD') === formatDate(this.curStartTime, 'YYYY-MM-DD');
                    }
                    // 检验有单子就可以时间对的上就行
                    return it.type === 1 && formatDate(it.planExecuteDate, 'YYYY-MM-DD') === formatDate(this.curStartTime, 'YYYY-MM-DD');
                });
            },
            examItem() {
                return this.examApplySheetReqsItem || {
                    chiefComplaint: '',
                    presentHistory: '',
                    physicalExamination: '',
                    purpose: '',
                    diagnosisInfos: [],
                };
            },
            examInfo: {
                get() {
                    const { examItem = {} } = this;
                    if (examItem?.purpose) {
                        const purpose = examItem?.purpose || '';
                        return `已填写: [目的]${purpose?.replaceAll('<br>','') || ''}`;
                    }
                    if (examItem?.diagnosisInfos?.length ||
                        examItem?.chiefComplaint ||
                        examItem?.presentHistory ||
                        examItem?.physicalExamination) {
                        return '已填写';
                    }
                    return '';
                },
                set() {},
            },
            placeholderStr() {
                return this.advice.type === AdviceRuleType.INSPECTION ? '填写检查申请' : '填写检验申请';
            },
            patientInfo() {
                return this.patient;
            },
            isInspect() {
                return this.advice.type === AdviceRuleType.INSPECTION;
            },
            examType() {
                return this.isInspect ? 2 : 1;
            },
            // 执行科室名称
            executeDepartmentName() {
                return this.advice.items[0].extendInfo?.executeDepartments?.[0]?.name || '';
            },
        },
        async created() {
            // 创建检验医嘱 根据各项信息获取当前检验单信息 同日期检验单共用一张 同日期检查单根据检查类型不同分属于不同单单据
            await this.fetchExamApplyInfoByPatientOrderId();
        },
        beforeDestroy() {
            this.supplementAdviceConfirm = null;
        },
        methods: {
            handleChangeTags(tags, isNeedConfirm = false) {
                if (tags.includes(AdviceTagEnum.SUPPLEMENT) && isNeedConfirm) {
                    new SupplementAdviceConfirm({
                        startTime: this.curStartTime,
                        stopTime: this.curStopTime,
                        adviceRemark: this.advice.remark,
                        adviceType: this.curType,
                        adviceRuleType: this.advice.type,
                        onConfirm: ({
                            startTime, stopTime, remark,
                        }) => {
                            this.curStartTime = startTime;
                            this.curStopTime = stopTime;
                            this.advice.remark = remark;
                            this.changeCurStartTime();
                        },
                        onCancel: () => {
                            this.groupItem.tagTypes = this.groupItem.tagTypes.filter((item) => item !== AdviceTagEnum.SUPPLEMENT);
                        },
                    }).generateDialog({
                        parent: this,
                    });
                }
                if (this.groupItem.tagTypes.includes(AdviceTagEnum.SUPPLEMENT) && !tags.includes(AdviceTagEnum.SUPPLEMENT)) {
                    this.curStopTime = '';
                    this.curStartTime = parseTime(new Date(), 'y-m-d h:i', true);
                }

                // 分离组级别和医嘱级别的tag
                const groupLevelTags = tags.filter((tag) => !AdviceLevelTags.includes(tag));
                const adviceLevelTags = tags.filter((tag) => AdviceLevelTags.includes(tag));

                // 更新组级别的tag
                this.groupItem.tagTypes = groupLevelTags;

                // 更新当前医嘱的tag
                this.$emit('update:tagTypes', adviceLevelTags);
            },
            changeCurStartTime() {
                this.$nextTick(() => {
                    if (!this.examApplySheetReqsItem) {
                        this.fetchExamApplyInfoByPatientOrderId('add');
                    } else {
                        this.$emit('refreshCheckExamApplySheetReqs');
                    }
                });
            },
            async fetchExamApplyInfoByPatientOrderId(type = 'modify') {
                // 如果有单子了则不创建了用之前的单子数据
                if (this.examApplySheetReqsItem) return;

                let examinationList = this.examAdviceGroups?.filter((item) => {
                    const { adviceRule } = item.advices[0];
                    if (this.isInspect && this.extendSpec === AdviceExaminationExtendSpec.DEFAULT) { // 如果是目前类型是检查需要类型相同
                        return item?.deviceType === this?.deviceType && item?.extendSpec === this.extendSpec && adviceRule.type === AdviceRuleType.INSPECTION;
                    }
                    if (this.isInspect && this.extendSpec !== AdviceExaminationExtendSpec.DEFAULT) { // 如果是目前类型是检查需要类型相同
                        return item?.extendSpec === this.extendSpec && adviceRule.type === AdviceRuleType.INSPECTION;
                    }
                    // 如果是检验则合并为一个单子
                    return adviceRule.type === AdviceRuleType.ASSAY;
                }) || [];
                // eslint-disable-next-line no-unused-vars
                examinationList = examinationList.map((item) => {
                    const { adviceRule } = item.advices[0];
                    return {
                        adviceName: adviceRule.items?.[0].name,
                        adviceId: adviceRule.items?.[0]?.goodsId,
                    };
                });
                const params = {
                    patientOrderId: this.patientOrderId,
                    departmentId: this.operateDepartmentId,
                    examDeviceType: this.deviceType,
                    examSubType: this.examType,
                    extendSpec: this.extendSpec,
                    startDate: formatDate(this.curStartTime, 'YYYY-MM-DD'),
                };
                try {
                    const { data } = await MedicalPrescriptionAPI.fetchExamApplyInfoByPatientOrderId(params);
                    const examApplySheetReqs = {
                        chiefComplaint: this.examItem?.chiefComplaint || data?.chiefComplaint || '',
                        deviceType: this.isInspect ? this.deviceType : '', // 检查才传检验不要
                        extendSpec: this.extendSpec,
                        diagnosisInfos: data?.diagnosisInfos || [],
                        id: data?.id || '',
                        physicalExamination: this.examItem?.physicalExamination || data?.physicalExamination || '',
                        presentHistory: this.examItem?.presentHistory || data?.presentHistory || '',
                        purpose: this.examItem?.purpose || data?.purpose || '',
                        type: this.examType,
                        planExecuteDate: formatDate(this.curStartTime, 'YYYY-MM-DD'),
                    };
                    this.$emit('addExamApply', examApplySheetReqs, type);
                    // end
                } catch (e) {
                    console.log(e);
                }
            },
            enterEvent(e) {
                this.$emit('enter', e);
            },
            isOpenExamDialog() {
                if (this.isNeedOpenAdviceSupplement) {
                    return false;
                }
                this.isShowExaminationDialog = true;
            },
            createExamApplySheetId(data) {
                const { examApplySheetReqs } = data;
                this.$emit('addExamApply', examApplySheetReqs);
            },
            async handleChangeItem(goods) {
                const adviceItem = MedicalPrescriptionService.getAdviceGoodsItem(goods);
                this.advice.items = [adviceItem];
                this.advice.type = MedicalPrescriptionService.getAdviceRuleType(goods);
                // 初始化医嘱的数据
                this.advice.singleDosageCount = 1;
                this.advice.singleDosageUnit = goods.packageUnit;
                this.advice.dosageCount = this.advice.singleDosageCount;
                this.advice.dosageUnit = this.advice.singleDosageUnit;
                this.advice.isDismounting = 0;
                this.advice.remark = '';
                this.isSearching = false;
            },

            handleDosageCountInput(v) {
                const reg = /0+(?=[1-9])|0+/g;
                const _v = v.replace(reg, '');
                this.$set(this.advice, 'dosageCount', _v);
            },
        },
    };
</script>
<style lang="scss">
@import "src/styles/theme.scss";
@import 'src/styles/mixin.scss';

.edit-medical-advice-item_exam--choice {
    color: $T2;
    cursor: pointer;
}
</style>

