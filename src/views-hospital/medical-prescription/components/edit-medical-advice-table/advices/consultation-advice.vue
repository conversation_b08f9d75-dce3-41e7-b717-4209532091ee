<template>
    <div class="edit-medical-advice-item tr">
        <div class="td table-sticky" :class="`width-${MPAddTable.checkbox.width}`">
            <div class="td-cell">
                <abc-checkbox v-model="advice.isChecked">
                </abc-checkbox>
            </div>
        </div>
        <div class="td table-sticky" :class="`width-${MPAddTable.type.width}`" :style="{ left: `${MPAddTable.checkbox.width}px` }">
            <abc-select
                v-model="dischargeType"
                disabled
                no-icon
                :width="48"
                :inner-width="100"
            >
                <abc-option
                    v-for="item in typeList"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                >
                </abc-option>
            </abc-select>
        </div>
        <div
            class="td table-sticky"
            :class="`width-${MPAddTable.name.width}`"
            :style="[MPAddTable.name.style, { left: `${MPAddTable.checkbox.width + MPAddTable.type.width}px` }]"
            :title="adviceName"
        >
            <div class="ellipsis" :style="MPAddTable.name.contentStyle">
                <span>{{ adviceName }}</span>
            </div>
        </div>
        <div
            class="td"
            :class="`width-${MPAddTable.medicalDetail.width}`"
            :style="MPAddTable.medicalDetail.style"
            @click="showConsultationDialog = true"
        >
            <abc-form-item required>
                <abc-input
                    v-model="consultationStr"
                    readonly
                    :width="322"
                    placeholder="填写会诊申请单"
                    @enter="enterEvent"
                ></abc-input>
            </abc-form-item>
        </div>
        <div class="td" :class="`width-${MPAddTable.remark.width}`">
            <advice-remark
                v-model="adviceCurrent.remark"
                placeholder="备注"
                :max-length="50"
                :remark-type="AdviceRemarkTypeEnum.Consultation"
                :tag-types="groupItem.tagTypes"
                :advice-tag-types="tagTypes"
                @change-tags="handleChangeTags"
            ></advice-remark>
        </div>
        <div class="td is-cell-disabled" :class="`width-${MPAddTable.dosageCount.width}`"></div>
        <div class="td" :class="`width-${MPAddTable.executeDepartment.width}`">
            <abc-select
                :show-value="curPatientHospitalInfo.wardName"
                :width="MPAddTable.executeDepartment.width"
                no-icon
                disabled
            ></abc-select>
        </div>
        <div class="td" :class="`width-${MPAddTable.startTime.width}`">
            <start-time
                v-model="curStartTime"
                :tag-types="groupItem.tagTypes"
                :advice-rule-type="advice.type"
                @enter="enterEvent"
            ></start-time>
        </div>
        <div class="td is-cell-disabled" :class="`width-${MPAddTable.stopTime.width}`"></div>
        <div class="td is-cell-disabled" :class="`width-${MPAddTable.executeTime.width}`">
        </div>
        <div
            class="td"
            :class="`width-${MPAddTable.products.width}`"
            :style="MPAddTable.products.style"
        >
            <relate-product
                :advice-type="curType"
                :advice="advice"
                is-related
                @change-pay-type="val => $set(advice, 'goodsIdMedicalInsurancePayTypes', val)"
            ></relate-product>
        </div>
        <consultation-details-dialog
            v-if="showConsultationDialog"
            v-model="showConsultationDialog"
            :consultation.sync="adviceCurrent.consultationSheet"
            :cur-start-time="curStartTime"
            :allow-supplement="allowOpenAdviceSupplement"
            :patient-order-id="patientOrderId"
            :patient="curPatientHospitalInfo"
        ></consultation-details-dialog>
    </div>
</template>

<script>
    import {
        MedicalAdviceTypeEnum,
        MedicalAdviceTypeStr,
        medicalPrescriptionOptionsEnum,
        AdviceRemarkTypeEnum,
        AdviceTagEnum,
        AdviceLevelTags,
    } from '@/views-hospital/medical-prescription/utils/constants';
    import { parseTime } from 'utils/index.js';
    import ConsultationDetailsDialog from '../../consultation/components/consultation-details-dialog.vue';
    import StartTime from '@/views-hospital/medical-prescription/components/advice-time/start-time.vue';
    import { MPAddTable } from '@/views-hospital/medical-prescription/model/mp-add-table.js';
    import AdviceRemark from '@/views-hospital/medical-prescription/components/advice-remark/index.vue';
    import { SupplementAdviceConfirm } from '@/views-hospital/medical-prescription/components/supplement-advice-confirm/supplement-advice-confirm.js';
    import RelateProduct
        from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/advices/related-product/relate-product.vue';
    export default {
        name: 'ConsultationAdvice',
        components: {
            ConsultationDetailsDialog,
            StartTime,
            AdviceRemark,
            RelateProduct,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            advice: {
                type: Object,
            },
            patientOrderId: {
                type: String,
                default: '',
            },
            startTime: String,
            type: [String, Number],
            stopTime: String,
            disabled: Boolean,
            groupItem: Object,
            tagTypes: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                MPAddTable,
                AdviceRemarkTypeEnum,
                medicalPrescriptionOptionsEnum,
                typeList: [
                    {
                        value: MedicalAdviceTypeEnum.ONE_TIME,
                        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.ONE_TIME],
                    },
                ],
                transferDepartment: '',
                showConsultationDialog: false,
                departments: [],
                allowSetStartTime: true,
            };
        },
        computed: {
            curType: {
                get() {
                    return this.type;
                },
                set(v) {
                    this.$emit('update:type', v);
                },
            },
            adviceName() {
                return this.adviceCurrent?.name;
            },
            curPatientHospitalInfo() {
                return this.$abcPage.$store.curPatientHospitalInfo;
            },
            allowOpenAdviceSupplement() {
                return !!this.groupItem?.tagTypes?.filter((item) => {
                    return item === AdviceTagEnum.SUPPLEMENT;
                }).length;
            },
            dischargeType: {
                get() {
                    return this.type;
                },
                set(v) {
                    this.$emit('update:type', v);
                },
            },
            adviceCurrent: {
                get() {
                    return this.advice;
                },
                set(v) {
                    this.$emit('update:advice', v);
                },
            },
            curStartTime: {
                get() {
                    return this.startTime;
                },
                set(v) {
                    this.$emit('update:startTime', v);
                },
            },
            curStopTime: {
                get() {
                    return this.stopTime;
                },
                set(v) {
                    this.$emit('update:stopTime', v);
                },
            },
            consultationStr() {
                if (!this.adviceCurrent.consultationSheet.participants.length) {
                    return '';
                }
                return `会诊申请: 「目的」${this.adviceCurrent.consultationSheet.purpose || '暂无'}`;
            },
            consultationSheetBeginTime() {
                return this.adviceCurrent?.consultationSheet?.beginTime;
            },
        },
        watch: {
            consultationSheetBeginTime(val) {
                if (this.allowSetStartTime) {
                    this.curStartTime = val;
                    this.allowSetStartTime = false;
                }
            },
        },
        mounted() {
            this.$nextTick(() => {
                this.showConsultationDialog = true;
            });
        },
        methods: {
            handleChangeTags(tags, isNeedConfirm = false) {
                if (tags.includes(AdviceTagEnum.SUPPLEMENT) && isNeedConfirm) {
                    new SupplementAdviceConfirm({
                        startTime: this.curStartTime,
                        stopTime: this.curStopTime,
                        adviceRemark: this.advice.remark,
                        adviceType: this.curType,
                        adviceRuleType: this.advice.type,
                        onConfirm: ({
                            startTime, stopTime, remark,
                        }) => {
                            this.curStartTime = startTime;
                            this.curStopTime = stopTime;
                            this.advice.remark = remark;
                        },
                        onCancel: () => {
                            this.groupItem.tagTypes = this.groupItem.tagTypes.filter((item) => item !== AdviceTagEnum.SUPPLEMENT);
                        },
                    }).generateDialog({
                        parent: this,
                    });
                }

                if (this.groupItem.tagTypes.includes(AdviceTagEnum.SUPPLEMENT) && !tags.includes(AdviceTagEnum.SUPPLEMENT)) {
                    this.curStopTime = '';
                    this.curStartTime = parseTime(new Date(), 'y-m-d h:i', true);
                }

                // 分离组级别和医嘱级别的tag
                const groupLevelTags = tags.filter((tag) => !AdviceLevelTags.includes(tag));
                const adviceLevelTags = tags.filter((tag) => AdviceLevelTags.includes(tag));

                // 更新组级别的tag
                this.groupItem.tagTypes = groupLevelTags;

                // 更新当前医嘱的tag
                this.$emit('update:tagTypes', adviceLevelTags);
            },
            enterEvent(e) {
                this.$emit('enter', e);
                this.$nextTick(() => {
                    $('.advice-product-autocomplete .abc-input__inner')?.eq(0)?.focus();
                });
            },
        },
    };
</script>

