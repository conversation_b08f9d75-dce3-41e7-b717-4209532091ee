<template>
    <div class="edit-medical-advice-item edit-treatment-advice-item tr">
        <div class="td table-sticky" :class="`width-${MPAddTable.checkbox.width}`">
            <div class="td-cell">
                <abc-checkbox v-model="advice.isChecked">
                </abc-checkbox>
            </div>
        </div>
        <div class="td table-sticky" :class="`width-${MPAddTable.type.width}`" :style="{ left: `${MPAddTable.checkbox.width}px` }">
            <abc-select
                v-model="curType"
                no-icon
                :width="MPAddTable.type.width"
                :inner-width="100"
                @change="handleChangeAdviceType"
            >
                <abc-option
                    v-for="item in typeList"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                >
                </abc-option>
            </abc-select>
        </div>
        <advice-name
            :advice="advice"
            :advice-type="curType"
            :patient="patient"
            :json-type-with-custom-type-list="jsonTypeWithCustomTypeList"
            @select="handleChangeItem"
        ></advice-name>

        <template v-if="isNurseLevel || !needFreq">
            <div class="td is-cell-disabled" :class="`width-${MPAddTable.medicalDetail.width}`" :style="MPAddTable.medicalDetail.style"></div>
        </template>
        <template v-else>
            <div class="td is-cell-disabled" :class="`width-${MPAddTable.usage.width}`" :style="MPAddTable.usage.style"></div>
            <div class="td" :class="`width-${MPAddTable.freq.width}`" :style="MPAddTable.freq.style">
                <div ref="freq-popper" style="position: relative;">
                    <div
                        ref="custom-freq-popper"
                        style="position: absolute; top: 0; left: 0; z-index: 2000;"
                    >
                        <custom-freq-dialog
                            v-if="showCustomFreq"
                            :custom-type="customFreq"
                            @close="closeCustomFreqDialog"
                            @confirm="confirmCustomFreqHandle"
                        ></custom-freq-dialog>
                    </div>
                    <freq
                        ref="freq"
                        :value="curFreq"
                        :advice-group-type="curType"
                        :width="58"
                        :advice-rule-type="advice.type"
                        @change="handleChangeFreqInfo"
                        @enter="enterEvent"
                    ></freq>
                </div>
            </div>
            <div class="td" :class="`width-${MPAddTable.singleDosageCount.width}`" :style="MPAddTable.singleDosageCount.style">
                <abc-form-item required>
                    <abc-input
                        v-model="advice.singleDosageCount"
                        v-abc-focus-selected
                        :width="46"
                        :input-custom-style="{ padding: '3px' }"
                        margin="0"
                        :max-length="4"
                        type="number"
                        class="count-center"
                        @change="handleChangeDosageData"
                        @enter="enterEvent"
                    >
                    </abc-input>
                </abc-form-item>
                <div class="input-append-unit" :title="advice.singleDosageUnit">
                    {{ advice.singleDosageUnit }}
                </div>
            </div>
            <div class="td" :class="[`width-${MPAddTable.days.width}`, { 'is-cell-disabled': !isLongTimeAdvice }]" :style="MPAddTable.days.style">
                <abc-form-item>
                    <abc-input
                        v-model="curDays"
                        v-abc-focus-selected
                        :disabled="!isLongTimeAdvice"
                        :width="32"
                        :input-custom-style="{ padding: '3px' }"
                        margin="0"
                        :max-length="4"
                        type="number"
                        class="count-center"
                        @enter="enterEvent"
                        @change="handleChangeDays"
                    >
                    </abc-input>
                </abc-form-item>
                <div class="input-append-unit">
                    天
                </div>
            </div>
            <div class="td is-cell-disabled" :class="`width-${MPAddTable.ast.width}`" :style="MPAddTable.ast.style"></div>
        </template>
        <div
            class="td"
            :class="`width-${MPAddTable.remark.width}`"
        >
            <advice-remark
                v-model="advice.remark"
                :remark-type="AdviceRemarkTypeEnum.Treatment"
                :prepend-info="getAcupointStr"
                show-append
                :tag-types="groupItem.tagTypes"
                :advice-tag-types="tagTypes"
                @icon-click="handleChangeAcupoint"
                @change-tags="handleChangeTags"
            ></advice-remark>
        </div>
        <div class="td" :class="`width-${MPAddTable.dosageCount.width} ${isLongTimeAdvice ? 'is-cell-disabled' : ''}`">
            <abc-form-item>
                <abc-input
                    v-model="advice.dosageCount"
                    v-abc-focus-selected
                    :disabled="isLongTimeAdvice"
                    :width="82"
                    :input-custom-style="{ padding: '3px' }"
                    margin="0"
                    :max-length="4"
                    type="number"
                    class="count-center"
                    @enter="enterEvent"
                >
                </abc-input>
            </abc-form-item>
            <div class="input-append-unit" :title="advice.dosageUnit">
                {{ advice.dosageUnit }}
            </div>
        </div>
        <div class="td" :class="`width-${MPAddTable.executeDepartment.width}`">
            <abc-select
                :show-value="curPatientHospitalInfo.wardName"
                :width="MPAddTable.executeDepartment.width"
                no-icon
                disabled
            ></abc-select>
        </div>
        <div class="td" :class="`width-${MPAddTable.startTime.width}`">
            <start-time
                v-model="curStartTime"
                :tag-types="groupItem.tagTypes"
                @enter="enterEvent"
                @change="handleChangeStartTime"
            ></start-time>
        </div>
        <div class="td" :class="[`width-${MPAddTable.stopTime.width}`, { 'is-cell-disabled': !isLongTimeAdvice }]">
            <stop-time
                v-if="isLongTimeAdvice"
                v-model="curStopTime"
                :start-time="curStartTime"
                @enter="enterEvent"
                @change="handleChangeStopTime"
            ></stop-time>
        </div>
        <div class="td" :class="`width-${MPAddTable.executeTime.width} ${curFreqInfo.code && !isNurseLevel ? '' : 'is-cell-disabled'}` ">
            <execute-time-select
                v-if="curFreqInfo.code "
                :freq-info="curFreqInfo"
            ></execute-time-select>
        </div>
        <div
            class="td"
            :class="`width-${MPAddTable.products.width}`"
            :style="MPAddTable.products.style"
        >
            <relate-product
                :advice="advice"
                :advice-type="curType"
                is-related
                @change-pay-type="val => $set(advice, 'goodsIdMedicalInsurancePayTypes', val)"
            ></relate-product>
        </div>
    </div>
</template>

<script>
    import {
        MedicalAdviceTypeEnum,
        MedicalAdviceTypeStr,
        ST_FREQ,
        AdviceRuleType,
        AdviceRemarkTypeEnum,
        AdviceTagEnum,
        AdviceLevelTags,
    } from '@/views-hospital/medical-prescription/utils/constants.js';
    import Freq from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/components/freq.vue';
    import ExecuteTimeSelect from '@/views-hospital/medical-prescription/components/execute-time-select/index.vue';
    import AcupointSelectDialog from 'src/views/layout/acupoint-select-dialog';
    import StartTime from '@/views-hospital/medical-prescription/components/advice-time/start-time.vue';
    import StopTime from '@/views-hospital/medical-prescription/components/advice-time/stop-time.vue';
    import AdviceName
        from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/advices/advice-name-td/advice-name.vue';
    import CustomFreqDialog from 'views/layout/prescription/common/custom-freq-dialog.vue';
    import RelateProduct
        from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/advices/related-product/relate-product.vue';
    import AdviceRemark from '@/views-hospital/medical-prescription/components/advice-remark/index.vue';

    import { MPAddTable } from '@/views-hospital/medical-prescription/model/mp-add-table.js';
    import { GoodsTypeIdEnum } from '@abc/constants';
    import { mapGetters } from 'vuex';
    import { SupplementAdviceConfirm } from '@/views-hospital/medical-prescription/components/supplement-advice-confirm/supplement-advice-confirm.js';
    import { MedicalPrescriptionService } from '@/views-hospital/medical-prescription/model/index.js';
    import {
        calcDaysByTimeDifference, calcStopTime,
        getFreqInfo,
        getFreqInfoByFirst,
    } from '@/views-hospital/medical-prescription/utils/format-advice.js';
    import { parseTime } from 'utils/index.js';

    import Popper from 'utils/vue-popper';

    export default {
        name: 'TreatmentAdvice',
        components: {
            Freq,
            ExecuteTimeSelect,
            StartTime,
            StopTime,
            AdviceName,
            CustomFreqDialog,
            RelateProduct,
            AdviceRemark,
        },
        mixins: [Popper],
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            advice: {
                type: Object,
            },
            type: [String, Number],
            usage: String,
            freq: {
                type: String,
            },
            freqInfo: [Object, null],
            days: [Number, String],
            disabled: Boolean,
            startTime: String,
            stopTime: String,
            patient: Object,
            groupItem: Object,
            tagTypes: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                MPAddTable,
                MedicalAdviceTypeEnum,
                AdviceRemarkTypeEnum,

                isSearching: false,
                curKeyword: '',
                showCustomFreq: false,
                customFreq: '',
            };
        },
        computed: {
            ...mapGetters('executeTime', ['allExecuteTimeList']),
            ...mapGetters(['currentClinic']),
            curPatientHospitalInfo() {
                return this.$abcPage.$store.curPatientHospitalInfo || {};
            },
            executeTimeList() {
                return this.allExecuteTimeList || [];
            },
            curUsage: {
                get() {
                    return this.usage;
                },
                set(v) {
                    this.$emit('update:usage', v);
                },
            },
            curFreq: {
                get() {
                    return this.freq;
                },
                set(v) {
                    this.$emit('update:freq', v);
                },
            },
            curDays: {
                get() {
                    return this.days;
                },
                set(v) {
                    this.$emit('update:days', v);
                },
            },
            curStartTime: {
                get() {
                    return this.startTime;
                },
                set(v) {
                    this.$emit('update:startTime', v);
                },
            },
            curStopTime: {
                get() {
                    return this.stopTime;
                },
                set(v) {
                    this.$emit('update:stopTime', v);
                },
            },
            curType: {
                get() {
                    return this.type;
                },
                set(v) {
                    this.$emit('update:type', v);
                },
            },
            curFreqInfo: {
                get() {
                    return this.freqInfo;
                },
                set(v) {
                    this.$emit('update:freqInfo', v);
                },
            },
            /**
             * @desc 是否是护理等级
             * <AUTHOR>
             * @date 2023-02-28 19:21:42
             */
            isNurseLevel() {
                return this.advice.type === AdviceRuleType.NURSE_LEVEL;
            },
            /**
             * @desc 是否是护理
             */
            isNurse() {
                return this.advice.type === AdviceRuleType.NURSE;
            },
            /**
             * @desc 是否是治疗
             * @return {boolean}
             */
            isTreatment() {
                return this.advice.type === AdviceRuleType.TREATMENT;
            },
            // 护理医嘱开启了住院执行需要填写频率
            needFreq() {
                if (this.isNurseLevel) {
                    return false;
                }
                if (this.isNurse) {
                    return this.advice.items[0].hospitalNeedExecutive;
                }
                if (this.isTreatment) {
                    return this.advice.items[0].hospitalNeedExecutive;
                }
                return true;
            },
            typeList() {
                return this.isNurseLevel ? [
                    {
                        value: MedicalAdviceTypeEnum.LONG_TIME,
                        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.LONG_TIME],
                    },
                ] : [
                    {
                        value: MedicalAdviceTypeEnum.ONE_TIME,
                        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.ONE_TIME],
                    },
                    {
                        value: MedicalAdviceTypeEnum.LONG_TIME,
                        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.LONG_TIME],
                    },
                ];
            },
            getAcupointStr() {
                const len = this.advice?.treatmentSites?.length || 0;
                return len > 3 ? `${this.advice.treatmentSites.slice(0,3).join(',')}等${len}穴位` :
                    this.advice?.treatmentSites?.join(',');
            },
            getAcupointStrDetail() {
                return this.advice?.treatmentSites?.join(',');
            },
            isLongTimeAdvice() {
                return this.curType === MedicalAdviceTypeEnum.LONG_TIME;
            },
            patientInfo() {
                return this.patient;
            },
            jsonTypeWithCustomTypeList() {
                return [
                    {
                        typeId: GoodsTypeIdEnum.TREATMENT_TREATMENT,
                    },
                    {
                        typeId: GoodsTypeIdEnum.NURSE,
                    },
                ];
            },
        },
        watch: {
            curType() {
                this.handleChangeDosageData();
            },
        },
        created() {
            this.$store.dispatch('initDoctorWesternPRRemarks');
            this.handleChangeDosageData();
        },
        mounted() {
            this.referenceElm = this.$refs?.['freq-popper'];
            this.popperElm = this.$refs?.['custom-freq-popper'];
            this.createPopper();
        },
        methods: {
            handleChangeTags(tags, isNeedConfirm = false) {
                if (tags.includes(AdviceTagEnum.SUPPLEMENT) && isNeedConfirm) {
                    new SupplementAdviceConfirm({
                        startTime: this.curStartTime,
                        stopTime: this.curStopTime,
                        adviceRemark: this.advice.remark,
                        adviceType: this.curType,
                        adviceRuleType: this.advice.type,
                        onConfirm: ({
                            startTime, stopTime, remark,
                        }) => {
                            this.curStartTime = startTime;
                            this.curStopTime = stopTime;
                            this.advice.remark = remark;
                        },
                        onCancel: () => {
                            this.groupItem.tagTypes = this.groupItem.tagTypes?.filter((item) => item !== AdviceTagEnum.SUPPLEMENT);
                        },
                    }).generateDialog({
                        parent: this,
                    });
                }
                if (this.groupItem.tagTypes.includes(AdviceTagEnum.SUPPLEMENT) && !tags.includes(AdviceTagEnum.SUPPLEMENT)) {
                    this.curStopTime = '';
                    this.curStartTime = parseTime(new Date(), 'y-m-d h:i', true);
                }

                // 分离组级别和医嘱级别的tag
                const groupLevelTags = tags.filter((tag) => !AdviceLevelTags.includes(tag));
                const adviceLevelTags = tags.filter((tag) => AdviceLevelTags.includes(tag));

                // 更新组级别的tag
                this.groupItem.tagTypes = groupLevelTags;

                // 更新当前医嘱的tag
                this.$emit('update:tagTypes', adviceLevelTags);
            },
            enterEvent(e, $el, value) {
                if ($el &&
                    value &&
                    $el.className.indexOf('abc-select-wrapper') > -1 &&
                    ['qnd', 'qnw', 'qnh'].indexOf(value) > -1) {
                    return false;
                }
                this.$emit('enter', e);
                // 找到所有的非disabled的input输入框
                const inputs = $('.edit-treatment-advice-item .is-required').find('.abc-input__inner').not(':disabled');
                const targetIndex = inputs.index(e.target);
                let nextInput = inputs[ targetIndex + 1 ];
                if (nextInput?.tabIndex === -1) {
                    nextInput = inputs[targetIndex + 2];
                }
                // 碰到连续tabindex===-1的情况，再加一次
                if (nextInput?.tabIndex === -1) {
                    nextInput = inputs[targetIndex + 3];
                }
                if (nextInput) {
                    this.$nextTick(() => {
                        nextInput.select && nextInput.select();
                        nextInput.focus && nextInput.focus();
                        // magic code
                        this._timer = setTimeout(() => {
                            nextInput.selectionStart = 0;
                            nextInput.selectionEnd = nextInput.value ? nextInput.value.length : 0;
                            nextInput.select && nextInput.select();
                            nextInput.focus && nextInput.focus();
                        }, 50);
                    });
                } else {
                    $('.advice-product-autocomplete .abc-input__inner')?.eq(0)?.focus();
                }
            },
            handleChangeAdviceType(val) {
                if (val === MedicalAdviceTypeEnum.ONE_TIME) {
                    this.curFreq = ST_FREQ;
                    this.advice.dosageCount = 1;
                    this.advice.dosageUnit = this.advice.items[0]?.packageUnit || '次';
                    this.$nextTick(() => {
                        this.handleChangeFreqInfo(this.curFreq);
                    });
                    this.curDays = 1;
                    this.curStopTime = '';
                } else {
                    this.curFreq = '';
                    this.curFreqInfo = getFreqInfo(this.executeTimeList, '', true, this.curStartTime);
                    this.curDays = null;
                }
            },
            handleChangeFreqInfo(val) {
                if (['qnd', 'qnw', 'nid'].includes(val)) {
                    this.customFreq = val;
                    this.$refs.freq.$refs.freqSelector.showPoper = false;
                    this.showCustomFreq = true;
                } else {
                    this.customFreq = '';
                    this.curFreq = val;
                    this.curFreqInfo = getFreqInfoByFirst(this.executeTimeList, val, true, this.curStartTime);
                }
            },
            closeCustomFreqDialog() {
                this.showCustomFreq = false;
            },
            confirmCustomFreqHandle(val) {
                if (val) {
                    this.curFreq = val;
                    Object.assign(this.curFreqInfo, getFreqInfoByFirst(this.executeTimeList, val, true, this.curStartTime));
                }
            },
            handleChangeAcupoint() {
                const selected = this.advice?.treatmentSites?.map((item) => {
                    return {
                        name: item,
                    };
                }) || [];
                new AcupointSelectDialog({
                    selected,
                    onChange: (acupoints) => {
                        this.advice.treatmentSites = acupoints.map((item) => {
                            return item.name;
                        });
                    },
                }).generateDialog({ parent: this });
            },
            handleChangeDosageData() {
                if (this.isLongTimeAdvice) {
                    this.advice.dosageCount = '';
                    this.advice.dosageUnit = '';
                }
                if (!this.isLongTimeAdvice) {
                    this.advice.dosageCount = this.advice.singleDosageCount;
                    this.advice.dosageUnit = this.advice.singleDosageUnit;
                }
            },
            async handleChangeItem(goods) {
                const adviceItem = MedicalPrescriptionService.getAdviceGoodsItem(goods);
                this.advice.items = [adviceItem];
                this.advice.type = MedicalPrescriptionService.getAdviceRuleType(goods);
                // 初始化医嘱的数据
                this.advice.singleDosageCount = 1;
                this.advice.singleDosageUnit = goods.packageUnit;
                this.advice.dosageUnit = goods.packageUnit;
                this.advice.isDismounting = 0;
                // 如果是护理等级的项目，需要改成长期医嘱，相关数据清空
                if (this.advice.type === AdviceRuleType.NURSE_LEVEL && this.curType === MedicalAdviceTypeEnum.ONE_TIME) {
                    this.curType = MedicalAdviceTypeEnum.LONG_TIME;
                    this.curDays = '';
                    this.curFreq = '';
                    this.curFreqInfo = '';
                }
                if (this.curType === MedicalAdviceTypeEnum.LONG_TIME) {
                    this.advice.dosageCount = '';
                    this.advice.dosageUnit = '';
                }
                this.isSearching = false;
            },
            handleChangeStartTime(val) {
                this.curFreqInfo = getFreqInfoByFirst(this.executeTimeList, this.curFreq, true, val);
            },
            /**
             * 长期医嘱改变天数, 重新计算结束时间和总量
             */
            handleChangeDays(days) {
                if (this.curStartTime) {
                    this.curStopTime = calcStopTime(this.curStartTime, days);
                }
            },
            /**
             * 长期医嘱修改停止时间, 重新计算总量
             */
            handleChangeStopTime(val) {
                if (val && this.curStartTime) {
                    this.curDays = calcDaysByTimeDifference(this.curStartTime, val);
                }
            },
        },
    };
</script>

