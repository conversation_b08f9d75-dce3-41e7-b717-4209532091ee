<template>
    <div class="edit-medical-advice-item tr">
        <div class="td table-sticky" :class="`width-${MPAddTable.checkbox.width}`">
            <div class="td-cell">
                <abc-checkbox v-model="advice.isChecked">
                </abc-checkbox>
            </div>
        </div>
        <div class="td table-sticky" :class="`width-${MPAddTable.type.width}`" :style="{ left: `${MPAddTable.checkbox.width}px` }">
            <abc-select
                v-model="curType"
                disabled
                no-icon
                :width="MPAddTable.type.width"
                :inner-width="100"
            >
                <abc-option
                    v-for="item in typeList"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                >
                </abc-option>
            </abc-select>
        </div>
        <advice-name
            :advice="advice"
            :advice-type="curType"
            :patient="patient"
            :json-type-with-custom-type-list="jsonTypeWithCustomTypeList"
            show-spec
            show-medical-fee-grade
            :validate-stock="!advice.items[0]?.chargeFlag"
            @select="handleChangeItem"
        ></advice-name>
        <div
            class="td is-cell-disabled"
            :class="`width-${MPAddTable.medicalDetail.width}`"
            :style="MPAddTable.medicalDetail.style"
        ></div>

        <div class="td" :class="`width-${MPAddTable.remark.width}`">
            <goods-remark
                ref="medicineRemarks"
                v-model="advice.remark"
                placeholder="备注"
                :max-length="50"
                placement="bottom-start"
                :readonly="false"
                support-tag
                :pr-form-item="advice.items[0]"
                :tabindex="-1"
                :ward-area-id="patientWardId"
                :tag-types="groupItem.tagTypes"
                :advice-tag-types="tagTypes"
                show-medicine-source-options
                show-no-charge
                show-psychotropic-narcotic-type
                @changeExtend="handleChangeSource"
                @change-tags="handleChangeTags"
                @enter="enterEvent"
            ></goods-remark>
        </div>
        <div class="td" :class="`width-${MPAddTable.dosageCount.width}`">
            <dosage
                :medicine.sync="advice.items[0]"
                placeholder="数量"
                required
                :input-width="82"
                :unit-width="36"
                :dosage-count.sync="advice.dosageCount"
                :dosage-unit.sync="advice.dosageUnit"
                :is-dismounting.sync="advice.dosageIsDismounting"
                :goods-dismounting="goodsDismounting"
                @changeCount="handleChangeDosageCount"
                @changeUnit="handleChangeDosageCountUnit"
                @enter="enterEvent"
            ></dosage>
        </div>
        <div ref="goodsPharmacySelector" class="td" :class="`width-${MPAddTable.executeDepartment.width}`">
            <abc-select
                :show-value="pharmacyName"
                :width="MPAddTable.executeDepartment.width"
                :inner-width="220"
                no-icon
                focus-show-options
                @change="handleChangeSource"
            >
                <abc-option
                    :value="{
                        id: null,
                        content: '自备',
                        sourceType: 1,
                    }"
                    label="自备"
                >
                    <span>
                        自备
                    </span>

                    <abc-text
                        size="mini"
                        theme="gray-light"
                        style="margin-left: auto;"
                    >
                        -
                    </abc-text>
                </abc-option>
                <abc-option
                    v-for="pharmacy in localPharmacyList"
                    :key="pharmacy.no"
                    :value="pharmacy"
                    :label="pharmacy.name"
                >
                    <span>
                        {{ pharmacy.name }}
                    </span>

                    <abc-text
                        v-if="defaultPharmacy && pharmacy.no === defaultPharmacy.no"
                        size="mini"
                        theme="gray-light"
                        style="margin-left: 4px;"
                    >
                        默认
                    </abc-text>

                    <abc-text
                        size="mini"
                        theme="gray-light"
                        style=" max-width: 60px; margin-left: auto;"
                        class="ellipsis"
                    >
                        {{ pharmacy.pharmacyTip }}
                    </abc-text>
                </abc-option>
            </abc-select>
        </div>
        <div class="td" :class="`width-${MPAddTable.startTime.width}`">
            <start-time
                v-model="curStartTime"
                :advice-rule-type="advice.type"
                :tag-types="groupItem.tagTypes"
            ></start-time>
        </div>
        <div class="td is-cell-disabled" :class="`width-${MPAddTable.stopTime.width}`">
        </div>
        <div class="td is-cell-disabled" :class="`width-${MPAddTable.executeTime.width}`">
        </div>
        <div class="td" :class="`width-${MPAddTable.products.width}`" :style="MPAddTable.products.style">
            <relate-product
                :advice="advice"
                :advice-type="curType"
                is-related
                is-medicine
                @change-pay-type="val => $set(advice, 'goodsIdMedicalInsurancePayTypes', val)"
            ></relate-product>
        </div>
    </div>
</template>

<script>
    import StartTime from '@/views-hospital/medical-prescription/components/advice-time/start-time.vue';
    import AdviceName
        from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/advices/advice-name-td/advice-name.vue';
    import RelateProduct
        from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/advices/related-product/relate-product.vue';
    import {
        MedicalAdviceTypeStr,
        MedicalAdviceTypeEnum,
        AdviceRemarkTypeEnum,
        AdviceTagEnum,
        AdviceLevelTags,
    } from '@/views-hospital/medical-prescription/utils/constants.js';
    import { MPAddTable } from '@/views-hospital/medical-prescription/model/mp-add-table.js';
    import { MedicalPrescriptionService } from '@/views-hospital/medical-prescription/model/index.js';
    import { GoodsTypeIdEnum } from '@abc/constants';
    import { mapGetters } from 'vuex';
    import { SupplementAdviceConfirm } from '@/views-hospital/medical-prescription/components/supplement-advice-confirm/supplement-advice-confirm.js';
    import { parseTime } from 'utils/index.js';
    import GoodsRemark from '@/views-hospital/medical-prescription/components/goods-remark/index.vue';
    import { OutpatientChargeTypeEnum } from 'views/outpatient/constants';
    import { resetStockByPharmacyNo } from 'views/layout/prescription/utils';
    import Dosage
        from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/components/dosage.vue';
    import { getSingleDosageUnitType } from '@/views-hospital/medical-prescription/utils/format-advice';
    import { getDefaultPharmacy } from 'views/common/pharmacy.js';

    export default {
        name: 'SurgeryAdvice',
        components: {
            Dosage,
            GoodsRemark,
            StartTime,
            AdviceName,
            RelateProduct,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            advice: {
                type: Object,
            },
            startTime: String,
            stopTime: String,
            type: [String, Number],
            disabled: Boolean,
            patient: {
                type: Object,
                required: true,
            },
            no: {
                type: [Number, String],
                default: 0,
            },
            groupItem: Object,
            tagTypes: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                MPAddTable,
                AdviceRemarkTypeEnum,
                typeList: [
                    {
                        value: MedicalAdviceTypeEnum.ONE_TIME,
                        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.ONE_TIME],
                    },
                ],
            };
        },
        computed: {
            ...mapGetters(['currentClinic', 'enableLocalPharmacyList', 'pharmacyRuleList']),
            jsonTypeWithCustomTypeList() {
                return [
                    {
                        typeId: GoodsTypeIdEnum.MATERIAL_MEDICINE_MATERIAL,
                    },
                    {
                        typeId: GoodsTypeIdEnum.ADDITIONAL_SELF_PRODUCT,
                    },
                    {
                        typeId: GoodsTypeIdEnum.ADDITIONAL_HEALTH_MEDICINE,
                    },
                    {
                        typeId: GoodsTypeIdEnum.ADDITIONAL_HEALTH_FOOD,
                    },
                    {
                        typeId: GoodsTypeIdEnum.ADDITIONAL_OTHER_PRODUCT,
                    },
                ];
            },
            groupItemHandle: {
                set(val) {
                    this.$emit('update:groupItem', val);
                },
                get() {
                    return this.groupItem;
                },
            },
            curType: {
                get() {
                    return this.type;
                },
                set(v) {
                    this.$emit('update:type', v);
                },
            },
            curStartTime: {
                get() {
                    return this.startTime;
                },
                set(v) {
                    this.$emit('update:startTime', v);
                },
            },
            curStopTime: {
                get() {
                    return this.stopTime;
                },
                set(v) {
                    this.$emit('update:stopTime', v);
                },
            },

            patientWardId() {
                return this.$abcPage.$store.currentPatientWardId;
            },

            defaultPharmacy() {
                return getDefaultPharmacy(this.pharmacyRuleList, {
                    wardAreaId: this.patientWardId,
                    goodsInfo: this.advice?.items[0],
                });
            },
            // 该物资 goods 是否可以拆零销售
            goodsDismounting() {
                return this.advice.items?.[0]?.dismounting ?? 1;
            },
            pharmacyName() {
                const { pharmacyNo } = this.advice;
                const pharmacyItem = this.enableLocalPharmacyList.find((it) => it.no === pharmacyNo);
                return pharmacyItem && pharmacyItem.name ? pharmacyItem.name : '自备';
            },
            curSelectProductInfo() {
                return this.advice?.items?.[0]?.productInfo || null;
            },

            localPharmacyList() {
                return (this.enableLocalPharmacyList || []).map((it) => {
                    if (!this.curSelectProductInfo) {
                        return {
                            ...it,
                            content: it.name,
                            sourceType: 1,
                        };
                    }

                    const {
                        packageUnit, pieceUnit, pharmacyGoodsStockList,
                    } = this.curSelectProductInfo;

                    if (!pharmacyGoodsStockList) {
                        return {
                            ...it,
                            content: it.name,
                            sourceType: 1,
                        };
                    }

                    const stock = pharmacyGoodsStockList.find((x) => x.pharmacyNo === it.no);
                    if (!stock) {
                        return {
                            ...it,
                            content: it.name,
                            sourceType: 1,
                            pharmacyTip: `0${packageUnit || pieceUnit}`,
                        };
                    }

                    let str = '';
                    if (packageUnit) {
                        str += `${stock.stockPackageCount || 0}${packageUnit}`;
                    }
                    if (pieceUnit) {
                        str += `${stock.stockPieceCount || 0}${pieceUnit}`;
                    }

                    return {
                        ...it,
                        content: it.name,
                        sourceType: 1,
                        pharmacyTip: str,
                    };
                });
            },
        },
        methods: {
            handleChangeSource(val) {
                const goods = {};
                if (val.id === null) {
                    goods.pharmacyType = '';
                    goods.pharmacyNo = '';
                    goods.pharmacyName = '';
                    goods.chargeFlag = OutpatientChargeTypeEnum.NO_CHARGE;
                    this.$nextTick(() => {
                        this.$Toast({
                            message: '备注选择【自备】，该药品将不会纳入划价收费',
                            duration: 1500,
                            referenceEl: this.$refs.goodsPharmacySelector,
                        });
                    });
                } else {
                    goods.pharmacyType = val.type;
                    goods.pharmacyNo = val.no;
                    goods.pharmacyName = val.name;
                    goods.chargeFlag = OutpatientChargeTypeEnum.DEFAULT;
                }
                Object.assign(this.advice.items[0], goods);
                this.$nextTick(() => {
                    resetStockByPharmacyNo(this.advice.items[0]);
                });
                // 后台取值是adviceRule上面的pharmacyNo
                this.$set(this.advice, 'pharmacyType', goods.pharmacyType);
                this.$set(this.advice, 'pharmacyNo', goods.pharmacyNo);
                this.$set(this.advice, 'pharmacyName', goods.pharmacyName);
            },
            handleChangeTags(tags, isNeedConfirm = false) {
                if (tags.includes(AdviceTagEnum.SUPPLEMENT) && isNeedConfirm) {
                    new SupplementAdviceConfirm({
                        startTime: this.curStartTime,
                        stopTime: this.curStopTime,
                        adviceRemark: this.advice.remark,
                        adviceType: this.curType,
                        adviceRuleType: this.advice.type,
                        onConfirm: ({
                            startTime, stopTime, remark,
                        }) => {
                            this.curStartTime = startTime;
                            this.curStopTime = stopTime;
                            this.advice.remark = remark;
                        },
                        onCancel: () => {
                            this.groupItem.tagTypes = this.groupItem.tagTypes.filter((item) => item !== AdviceTagEnum.SUPPLEMENT);
                        },
                    }).generateDialog({
                        parent: this,
                    });
                }
                if (this.groupItem.tagTypes.includes(AdviceTagEnum.SUPPLEMENT) && !tags.includes(AdviceTagEnum.SUPPLEMENT)) {
                    this.curStopTime = '';
                    this.curStartTime = parseTime(new Date(), 'y-m-d h:i', true);
                }

                // 分离组级别和医嘱级别的tag
                const groupLevelTags = tags.filter((tag) => !AdviceLevelTags.includes(tag));
                const adviceLevelTags = tags.filter((tag) => AdviceLevelTags.includes(tag));

                // 更新组级别的tag
                this.groupItem.tagTypes = groupLevelTags;

                // 更新当前医嘱的tag
                this.$emit('update:tagTypes', adviceLevelTags);
            },
            enterEvent(e) {
                this.$emit('enter', e);
            },
            async handleChangeItem(goods) {
                const adviceItem = MedicalPrescriptionService.getAdviceGoodsItem(goods);
                this.advice.items = [adviceItem];
                this.advice.type = MedicalPrescriptionService.getAdviceRuleType(goods);
                // 初始化医嘱的数据
                this.advice.singleDosageCount = 1;
                this.advice.singleDosageUnit = goods.packageUnit;
                this.advice.dosageCount = 1;
                this.advice.dosageUnit = goods.packageUnit;
                this.advice.isDismounting = 0;
                this.advice.remark = '';
            },
            /**
             * 修改总量时需要同时修改单次用量
             */
            handleChangeDosageCount(val) {
                this.$set(this.advice, 'singleDosageCount', parseFloat(val));
            },
            /**
             * 1. 切换总量单位时需进行换算
             * 2. 切换总量单位需要同步切换单次用量单位
             */
            handleChangeDosageCountUnit(val, oldVal) {
                if (!this.advice || !this.advice.items?.[0]) {
                    return;
                }

                const {
                    pieceNum,
                    packageUnit,
                    pieceUnit,
                } = this.advice.items[0];

                // 如果原来没有选择单位, 则不需要进行换算
                if (!oldVal) {
                    this.$nextTick(() => {
                        if (!val) {
                            // 选择大单位
                            this.$set(this.advice, 'singleDosageUnit', packageUnit);
                            this.$set(this.advice, 'singleDosageUnitType', getSingleDosageUnitType(packageUnit, this.advice.items[0]));
                        } else {
                            // 选择小单位
                            this.$set(this.advice, 'singleDosageUnit', pieceUnit);
                            this.$set(this.advice, 'singleDosageUnitType', getSingleDosageUnitType(pieceUnit, this.advice.items[0]));
                        }
                    });
                    return;
                }

                // 从小单位切换到包装单位(大单位)
                let dosageCount = parseInt(this.advice.dosageCount);
                dosageCount = isNaN(dosageCount) ? 1 : dosageCount;

                this.$nextTick(() => {
                    if (!val) {
                        // 切换到大单位
                        const ret = Math.ceil(dosageCount / pieceNum);
                        this.$set(this.advice, 'dosageCount', ret);
                        this.$set(this.advice, 'singleDosageCount', ret);
                        this.$set(this.advice, 'singleDosageUnit', packageUnit);
                        this.$set(this.advice, 'singleDosageUnitType', getSingleDosageUnitType(packageUnit, this.advice.items[0]));
                    } else {
                        // 切换到小单位
                        const ret = Math.ceil(dosageCount * pieceNum);
                        this.$set(this.advice, 'dosageCount', ret);
                        this.$set(this.advice, 'singleDosageCount', ret);
                        this.$set(this.advice, 'singleDosageUnit', pieceUnit);
                        this.$set(this.advice, 'singleDosageUnitType', getSingleDosageUnitType(pieceUnit, this.advice.items[0]));
                    }
                });
            },
        },
    };
</script>
<style lang="scss">
@import "src/styles/theme.scss";
@import 'src/styles/mixin.scss';

.edit-medical-advice-item_exam--choice {
    color: $T2;
    cursor: pointer;
}
</style>
