<template>
    <div class="edit-medical-advice-item tr">
        <div class="td table-sticky" :class="`width-${MPAddTable.checkbox.width}`">
            <div class="td-cell">
                <abc-checkbox v-model="advice.isChecked">
                </abc-checkbox>
            </div>
        </div>
        <div class="td table-sticky" :class="`width-${MPAddTable.type.width}`" :style="{ left: `${MPAddTable.checkbox.width}px` }">
            <abc-select
                v-model="curType"
                no-icon
                :width="48"
                :inner-width="100"
                @change="handleChangeAdviceType"
            >
                <abc-option
                    v-for="item in typeList"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                >
                </abc-option>
            </abc-select>
        </div>
        <div
            class="td table-sticky"
            :class="`width-${MPAddTable.name.width}`"
            :style="[MPAddTable.name.style, { left: `${MPAddTable.checkbox.width + MPAddTable.type.width}px` }]"
        >
            <div class="ellipsis" style="display: flex; width: 100%;">
                <abc-form-item required>
                    <abc-select
                        v-model="advice.type"
                        :width="52"
                        :inner-width="60"
                        no-icon
                        placeholder="类别"
                        :style="`border-right: 1px dashed ${$store.state.theme.style.P6}` "
                        @change="handleSetDiagnosisTreatmentType"
                    >
                        <abc-option
                            v-for="item in adviceOptions"
                            :key="item.value"
                            :value="item.value"
                            :label="item.label"
                        ></abc-option>
                    </abc-select>
                </abc-form-item>
                <abc-form-item required style="width: 100%;">
                    <abc-input
                        v-model="advice.name"
                        class="hand-written-advice-name"
                        style="width: 100%;"
                        placeholder="[手写]嘱托内容"
                        :input-custom-style="{
                            'width': '100%',
                            'paddingLeft': advice.name ? '52px !important' : '10px !important'
                        }"
                    >
                        <div v-if="advice.name" slot="prepend" style="display: flex; align-items: center; color: #5d92d2;">
                            <span style="margin-top: -1px;">
                                [
                            </span>
                            手写
                            <span style="margin-top: -1px;">
                                ]
                            </span>
                        </div>
                    </abc-input>
                </abc-form-item>
            </div>
        </div>
        <div
            v-if="disabledAdviceDetail"
            class="td"
            :class="[`width-${MPAddTable.medicalDetail.width}`, { 'is-cell-disabled': disabledAdviceDetail }]"
            :style="MPAddTable.medicalDetail.style"
        >
        </div>
        <template v-else>
            <div class="td" :class="[`width-${MPAddTable.usage.width}`, { 'is-cell-disabled': !isMedicineTreatmentType }]" :style="MPAddTable.usage.style">
                <select-group
                    v-if="isMedicineTreatmentType"
                    v-model="curUsage"
                    :disabled="!isMedicineTreatmentType"
                    type="usages"
                    placeholder="用法"
                ></select-group>
            </div>
            <div class="td" :class="`width-${MPAddTable.freq.width}`" :style="MPAddTable.freq.style">
                <div style="position: relative;">
                    <freq
                        ref="freq"
                        :required="!isNurseType"
                        :value="curFreq"
                        :advice-group-type="curType"
                        :width="58"
                        :advice-rule-type="advice.type"
                        :created-type="DoctorMedicalPrescriptionTypeEnum.WRITTEN"
                        @enter="enterEvent"
                        @change="handleChangeFreqInfo"
                    ></freq>
                    <custom-freq-dialog
                        v-if="showCustomFreq"
                        :custom-type="customFreq"
                        @close="closeCustomFreqDialog"
                        @confirm="confirmCustomFreqHandle"
                    ></custom-freq-dialog>
                </div>
            </div>
            <div class="td" :class="[`width-${MPAddTable.singleDosageCount.width}`]" :style="MPAddTable.singleDosageCount.style">
                <single-dosage
                    :required="false"
                    :medicine="{}"
                    placeholder="单次"
                    :is-treatment-type="isTreatmentType"
                    :input-width="50"
                    :unit-width="36"
                    :single-dosage-count.sync="advice.singleDosageCount"
                    :single-dosage-unit.sync="advice.singleDosageUnit"
                    :single-dosage-unit-type.sync="advice.singleDosageUnitType"
                    @enter="enterEvent"
                >
                </single-dosage>
            </div>
            <div
                class="td"
                :class="[`width-${MPAddTable.days.width}` ,{ 'is-cell-disabled': isOneTimeMedical }]"
                :style="MPAddTable.days.style"
            >
                <template v-if="!isOneTimeMedical">
                    <abc-form-item>
                        <abc-input
                            v-model="curDays"
                            v-abc-focus-selected
                            :width="32"
                            :input-custom-style="{ padding: '3px' }"
                            margin="0"
                            :max-length="4"
                            type="number"
                            class="count-center"
                            @enter="enterEvent"
                            @change="handleChangeTime('days')"
                        >
                        </abc-input>
                    </abc-form-item>
                    <div class="input-append-unit">
                        天
                    </div>
                </template>
            </div>
            <div class="td " :class="[`width-${MPAddTable.ast.width}`, { 'is-cell-disabled': !isMedicineTreatmentType }]" :style="MPAddTable.ast.style">
                <ast-td
                    :width="MPAddTable.ast.width"
                    class="table-td ast"
                    :disabled="!isMedicineTreatmentType"
                    :item="advice"
                ></ast-td>
            </div>
        </template>
        <div class="td" :class="`width-${MPAddTable.remark.width}`">
            <advice-remark
                v-model="advice.remark"
                placeholder="备注"
                :max-length="50"
                :remark-type="AdviceRemarkTypeEnum.Discharge"
                :tag-types="groupItem.tagTypes"
                :advice-tag-types="tagTypes"
                @change-tags="handleChangeTags"
            ></advice-remark>
        </div>
        <div class="td" :class="[`width-${MPAddTable.dosageCount.width}`, { 'is-cell-disabled': !isInputDosageCount }]">
            <template v-if="isInputDosageCount">
                <abc-form-item>
                    <abc-input
                        v-model="advice.dosageCount"
                        v-abc-focus-selected
                        :width="82"
                        :input-custom-style="{ padding: '3px' }"
                        margin="0"
                        :max-length="5"
                        :config="{
                            min: 1, max: 99999, formatLength: 0
                        }"
                        type="number"
                        class="count-center"
                        @enter="enterEvent"
                    >
                    </abc-input>
                </abc-form-item>
                <abc-form-item v-if="isAllowSelectUsage">
                    <!--系统药品：显示domain表的所有单位-->
                    <select-usage
                        v-model="advice.dosageUnit"
                        v-abc-focus-selected
                        :type="isMaterialsType ? 'materialUnit' : 'allDosageUnit'"
                        focus-show-options
                        :disabled="disabled"
                        :width="36"
                        @enter="enterEvent"
                    >
                    </select-usage>
                </abc-form-item>
                <div v-else class="input-append-unit">
                    次
                </div>
            </template>
        </div>
        <div class="td" :class="`width-${MPAddTable.executeDepartment.width}`">
            <abc-select
                :show-value="curPatientHospitalInfo.wardName"
                :width="MPAddTable.executeDepartment.width"
                no-icon
                disabled
            ></abc-select>
        </div>
        <div class="td" :class="`width-${MPAddTable.startTime.width}`">
            <start-time
                v-model="curStartTime"
                :tag-types="groupItem.tagTypes"
                :advice-rule-type="advice.type"
                @enter="enterEvent"
                @change="handleChangeTime('startTime')"
            ></start-time>
        </div>
        <div class="td" :class="[`width-${MPAddTable.stopTime.width}`, { 'is-cell-disabled': isOneTimeMedical || (!isMedicineTreatmentType && !isTreatmentType && !isNurseType) }]">
            <stop-time
                v-if="!isOneTimeMedical && (isMedicineTreatmentType || isTreatmentType || isNurseType)"
                v-model="curStopTime"
                :start-time="curStartTime"
                :required="false"
                @change="handleChangeTime('stopTime')"
            ></stop-time>
        </div>
        <div class="td is-cell-disabled" :class="`width-${MPAddTable.executeTime.width}`">
        </div>
        <div class="td" :class="`width-${MPAddTable.products.width}`" :style="MPAddTable.products.style">
        </div>
    </div>
</template>

<script>
    import {
        AdviceRemarkTypeEnum,
        AdviceTagEnum,
        AdviceLevelTags,
        MedicalAdviceTypeEnum,
        MedicalAdviceTypeStr,
        medicalPrescriptionOptionsEnum,
        AdviceRuleType,
        TreatmentTypeEnum,
        ST_FREQ,
        DoctorMedicalPrescriptionTypeEnum,
    } from '@/views-hospital/medical-prescription/utils/constants';
    import StartTime from '@/views-hospital/medical-prescription/components/advice-time/start-time.vue';
    import AdviceRemark from '@/views-hospital/medical-prescription/components/advice-remark/index.vue';
    import SelectGroup from '@/views/layout/select-group/index.vue';
    import AstTd from '../components/ast-td.vue';
    import Freq from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/components/freq.vue';
    import CustomFreqDialog from 'views/layout/prescription/common/custom-freq-dialog.vue';
    import SingleDosage from '../components/single-dosage.vue';
    import SelectUsage from '@/views/layout/select-group/index.vue';

    import SettingAPI from 'api/settings';

    import { mapGetters } from 'vuex';

    import { MPAddTable } from '@/views-hospital/medical-prescription/model/mp-add-table.js';
    import {
        SupplementAdviceConfirm,
    } from '@/views-hospital/medical-prescription/components/supplement-advice-confirm/supplement-advice-confirm.js';
    import {
        isNull, parseTime,
    } from 'utils/index.js';
    import { MedicalPrescriptionService } from '@/views-hospital/medical-prescription/model/index.js';
    import {
        calcDaysByTimeDifference, calcStopTime, getFreqInfoByFirst,
    } from '@/views-hospital/medical-prescription/utils/format-advice.js';
    import StopTime from '@/views-hospital/medical-prescription/components/advice-time/stop-time.vue';


    export default {
        name: 'HandWrittenAdvice',
        components: {
            StopTime,
            StartTime,
            AdviceRemark,
            SelectGroup,
            AstTd,
            Freq,
            CustomFreqDialog,
            SingleDosage,
            SelectUsage,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            advice: {
                type: Object,
            },
            usage: String,
            freq: {
                type: String,
            },
            freqInfo: [Object, null],
            days: [Number, String],
            startTime: String,
            type: [String, Number],
            stopTime: String,
            disabled: Boolean,
            groupItem: Object,
            diagnosisTreatmentType: {
                type: [Number, String],
            },
            dispenseType: Number,
            tagTypes: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                AdviceRemarkTypeEnum,
                MPAddTable,
                medicalPrescriptionOptionsEnum,
                DoctorMedicalPrescriptionTypeEnum,
                transferDepartment: '',
                departments: [],
                showCustomFreq: false,
                customFreq: '',
            };
        },
        computed: {
            ...mapGetters('executeTime', ['allExecuteTimeList']),
            curPatientHospitalInfo() {
                return this.$abcPage.$store.curPatientHospitalInfo || {};
            },
            curType: {
                get() {
                    return this.type;
                },
                set(v) {
                    this.$emit('update:type', v);
                },
            },
            curFreqInfo: {
                get() {
                    return this.freqInfo;
                },
                set(v) {
                    this.$emit('update:freqInfo', v);
                },
            },
            adviceCurrent: {
                get() {
                    return this.advice;
                },
                set(v) {
                    this.$emit('update:advice', v);
                },
            },
            curStartTime: {
                get() {
                    return this.startTime;
                },
                set(v) {
                    this.$emit('update:startTime', v);
                },
            },
            curStopTime: {
                get() {
                    return this.stopTime;
                },
                set(v) {
                    this.$emit('update:stopTime', v);
                },
            },
            curDays: {
                get() {
                    return this.days;
                },
                set(v) {
                    this.$emit('update:days', v);
                },
            },
            curUsage: {
                get() {
                    return this.usage;
                },
                set(v) {
                    this.$emit('update:usage', v);
                },
            },
            curFreq: {
                get() {
                    return this.freq;
                },
                set(v) {
                    this.$emit('update:freq', v);
                },
            },
            curDispenseType: {
                get() {
                    return this.dispenseType;
                },
                set(v) {
                    this.$emit('update:dispenseType', v);
                },
            },
            isLongTimeMedical() {
                return this.curType === MedicalAdviceTypeEnum.LONG_TIME;
            },
            isOneTimeMedical() {
                return this.curType === MedicalAdviceTypeEnum.ONE_TIME;
            },
            transferToDepartmentName() {
                if (!this.departments?.length) {
                    return '';
                }
                const departmentsName = this.departments?.find((item) => {
                    return item.id === this.adviceCurrent.transferToDepartment;
                })?.name || '';
                return departmentsName;
            },

            curDiagnosisTreatmentType: {
                get() {
                    return this.diagnosisTreatmentType;
                },
                set(v) {
                    this.$emit('update:diagnosisTreatmentType', v);
                },
            },
            longTimeAdvice() {
                return [
                    AdviceRuleType.WESTERN_MEDICINE,
                    AdviceRuleType.NURSE,
                    AdviceRuleType.TREATMENT,
                ];
            },
            dischargeWithMedicineAdvice() {
                return [
                    AdviceRuleType.WESTERN_MEDICINE,
                ];
            },
            typeList() {
                const list = [{
                    value: MedicalAdviceTypeEnum.ONE_TIME,
                    label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.ONE_TIME],
                }];
                if (this.longTimeAdvice.includes(this.advice.type) || isNull(this.advice.type)) {
                    list.push(
                        {
                            value: MedicalAdviceTypeEnum.LONG_TIME,
                            label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.LONG_TIME],
                        },
                    );
                }
                if (this.dischargeWithMedicineAdvice.includes(this.advice.type) || isNull(this.advice.type)) {
                    list.push(
                        {
                            value: MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE,
                            label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE],
                        },
                    );
                }


                return list;
            },
            adviceOptions() {
                return [
                    {
                        value: AdviceRuleType.WESTERN_MEDICINE,
                        label: '药品',
                    },
                    {
                        value: AdviceRuleType.NURSE,
                        label: '护理',
                    },
                    {
                        value: AdviceRuleType.TREATMENT,
                        label: '治疗',
                    },
                    {
                        value: AdviceRuleType.ASSAY,
                        label: '检验',
                    },
                    {
                        value: AdviceRuleType.INSPECTION,
                        label: '检查',
                    },
                    {
                        value: AdviceRuleType.CONSULTATION,
                        label: '会诊',
                    },
                    {
                        value: AdviceRuleType.SURGERY,
                        label: '手术',
                    },
                    {
                        value: AdviceRuleType.MEDICINE_MATERIAL,
                        label: '物资',
                    },
                ];
            },

            disabledAdviceDetail() {
                return [
                    AdviceRuleType.INSPECTION,
                    AdviceRuleType.ASSAY,
                    AdviceRuleType.CONSULTATION,
                    AdviceRuleType.SURGERY,
                    AdviceRuleType.MEDICINE_MATERIAL,
                ].includes(this.advice.type) || isNull(this.advice.type);
            },

            isMedicineTreatmentType() {
                return this.curDiagnosisTreatmentType === TreatmentTypeEnum.MEDICINE;
            },
            isNurseType() {
                return this.advice.type === AdviceRuleType.NURSE;
            },
            isInputDosageCount() {
                return this.isMedicineTreatmentType ||
                    this.isSurgeryType ||
                    [TreatmentTypeEnum.ASSAY, TreatmentTypeEnum.INSPECTION, TreatmentTypeEnum.MATERIALS].includes(this.curDiagnosisTreatmentType);
            },
            isTreatmentType() {
                return this.advice.type === AdviceRuleType.TREATMENT;
            },
            isSurgeryType() {
                return this.curDiagnosisTreatmentType === TreatmentTypeEnum.SURGERY;
            },
            isMaterialsType() {
                return this.curDiagnosisTreatmentType === TreatmentTypeEnum.MATERIALS;
            },
            isAllowSelectUsage() {
                return this.isMedicineTreatmentType || this.isMaterialsType;
            },
        },
        watch: {
            // 分组的频率变更
            curFreq(val) {
                this.curFreqInfo = getFreqInfoByFirst(this.allExecuteTimeList, val, true, this.curStartTime);
            },
            curStartTime(v) {
                this.adviceCurrent.stopLongAdviceTime = v || '';
            },
        },
        async created() {
            await this.fetchDepartments();
        },
        methods: {
            enterEvent(e) {
                this.$emit('enter', e);
                this.$nextTick(() => {
                    $('.advice-product-autocomplete .abc-input__inner')?.eq(0)?.focus();
                });
            },
            async fetchDepartments() {
                try {
                    const res = await SettingAPI.clinic.fetchClinicDepartments({ type: 1 });
                    const { data } = res.data;
                    this.departments = data?.rows || [];
                } catch (e) {
                    console.log('科室信息获取错误', e);
                }
            },
            transfer(item) {
                const {
                    transferToDepartment, remark,
                } = item;
                const params = {
                    transferToDepartment,
                    remark,
                };
                this.adviceCurrent.stopLongAdviceTime = this.curStartTime;
                this.adviceCurrent = Object.assign(this.adviceCurrent, params);
            },
            handleChangeFreqInfo(val) {
                if (['qnd', 'qnw', 'nid'].includes(val)) {
                    this.customFreq = val;
                    this.$refs.freq.$refs.freqSelector.showPoper = false;
                    this.showCustomFreq = true;
                } else {
                    this.customFreq = '';
                    this.curFreq = val;
                }
            },
            closeCustomFreqDialog() {
                this.showCustomFreq = false;
            },
            confirmCustomFreqHandle(val) {
                if (val) {
                    this.curFreq = val;
                }
            },
            handleChangeTags(tags, isNeedConfirm = false) {
                if (tags.includes(AdviceTagEnum.SUPPLEMENT) && isNeedConfirm) {
                    new SupplementAdviceConfirm({
                        startTime: this.curStartTime,
                        stopTime: this.curStopTime,
                        adviceRemark: this.advice.remark,
                        adviceType: this.curType,
                        adviceRuleType: this.advice.type,
                        onConfirm: ({
                            startTime, stopTime, remark,
                        }) => {
                            this.curStartTime = startTime;
                            this.curStopTime = stopTime;
                            this.advice.remark = remark;
                        },
                        onCancel: () => {
                            this.groupItem.tagTypes = this.groupItem.tagTypes.filter((item) => item !== AdviceTagEnum.SUPPLEMENT);
                        },
                    }).generateDialog({
                        parent: this,
                    });
                }
                if (this.groupItem.tagTypes.includes(AdviceTagEnum.SUPPLEMENT) && !tags.includes(AdviceTagEnum.SUPPLEMENT)) {
                    this.curStopTime = '';
                    this.curStartTime = parseTime(new Date(), 'y-m-d h:i', true);
                }

                // 分离组级别和医嘱级别的tag
                const groupLevelTags = tags.filter((tag) => !AdviceLevelTags.includes(tag));
                const adviceLevelTags = tags.filter((tag) => AdviceLevelTags.includes(tag));

                // 更新组级别的tag
                this.groupItem.tagTypes = groupLevelTags;

                // 更新当前医嘱的tag
                this.$emit('update:tagTypes', adviceLevelTags);
            },
            /**
             * @desc 更新分组上的医嘱类型
             */
            handleSetDiagnosisTreatmentType(val) {
                this.curDiagnosisTreatmentType = MedicalPrescriptionService.getDiagnosisTreatmentType(val);
                this.resetGroupData();
                if ([AdviceRuleType.WESTERN_MEDICINE, AdviceRuleType.TREATMENT].includes(val)) {
                    if (this.curType === MedicalAdviceTypeEnum.ONE_TIME) {
                        this.curFreq = ST_FREQ;
                        this.curDays = 1;
                        this.$nextTick(() => {
                            this.handleChangeFreqInfo(this.curFreq);
                        });
                    } else {
                        this.curFreq = 'qd';
                        this.curDays = null;
                        this.$nextTick(() => {
                            this.handleChangeFreqInfo(this.curFreq);
                        });
                    }
                }
            },
            resetGroupData() {
                this.curFreq = '';
                this.curDays = '';
                this.curUsage = '';
                this.curStopTime = '';
                this.advice.remark = '';
                this.advice.dosageUnit = '';
                this.curDispenseType = null;
                if (this.curType === MedicalAdviceTypeEnum.LONG_TIME && !this.longTimeAdvice.includes(this.advice.type)) {
                    this.curType = MedicalAdviceTypeEnum.ONE_TIME;
                } else if (this.curType === MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE && !this.dischargeWithMedicineAdvice.includes(this.advice.type)) {
                    this.curType = MedicalAdviceTypeEnum.ONE_TIME;
                }
                if ([TreatmentTypeEnum.ASSAY, TreatmentTypeEnum.INSPECTION, TreatmentTypeEnum.SURGERY].includes(this.curDiagnosisTreatmentType)) {
                    this.advice.dosageUnit = '次';
                }
                this.$nextTick(() => {
                    if ([TreatmentTypeEnum.MEDICINE].includes(this.curDiagnosisTreatmentType)) {
                        this.curDispenseType = 1;
                    }
                });
            },
            handleChangeAdviceType(val) {
                if ([AdviceRuleType.WESTERN_MEDICINE, AdviceRuleType.TREATMENT].includes(this.advice.type)) {
                    if (val === MedicalAdviceTypeEnum.ONE_TIME) {
                        this.curFreq = ST_FREQ;
                        this.curDays = 1;
                        this.$nextTick(() => {
                            this.handleChangeFreqInfo(this.curFreq);
                        });
                        this.curDays = 1;
                    } else {
                        this.curFreq = 'qd';
                        this.curDays = null;
                        this.$nextTick(() => {
                            this.handleChangeFreqInfo(this.curFreq);
                        });
                    }
                }
            },
            /**
             * 天数/开始时间/停止时间相互计算
             */
            async handleChangeTime(type = 'days') {
                await this.$nextTick();
                if (type === 'days') {
                    if (this.curStartTime) {
                        this.curStopTime = calcStopTime(this.curStartTime, this.curDays);
                    }
                    return;
                }
                if (this.curStartTime && this.curStopTime) {
                    this.curDays = calcDaysByTimeDifference(this.curStartTime, this.curStopTime);
                }
            },
        },
    };
</script>
<style lang="scss">
.edit-medical-advice-item {
    .hand-written-advice-name.abc-input-wrapper .prepend-input {
        width: 52px;
        padding-left: 10px;
    }
}
</style>

