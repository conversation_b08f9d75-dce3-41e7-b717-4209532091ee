<template>
    <div class="edit-medical-advice-item tr">
        <div class="td table-sticky" :class="`width-${MPAddTable.checkbox.width}`">
            <div class="td-cell">
                <abc-checkbox v-model="advice.isChecked">
                </abc-checkbox>
            </div>
        </div>
        <div class="td table-sticky" :class="`width-${MPAddTable.type.width}`" :style="{ left: `${MPAddTable.checkbox.width}px` }">
            <abc-select
                v-model="dischargeType"
                disabled
                no-icon
                :width="48"
                :inner-width="48"
            >
                <abc-option
                    v-for="item in typeList"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                >
                </abc-option>
            </abc-select>
        </div>
        <advice-name
            :advice="advice"
            :patient="patient"
            :advice-type="dischargeType"
            :json-type-with-custom-type-list="jsonTypeWithCustomTypeList"
            @select="handleChangeMedicine"
        ></advice-name>

        <div class="td" :class="`width-${MPAddTable.medicalDetail.width}`" :style="MPAddTable.medicalDetail.style">
            <discharge-select
                :patient-order-id="patientOrderId"
                :width="322"
                :placeholder="placeholder"
                :is-transfer="isTransfer"
                :start-time.sync="curStartTime"
                :advice.sync="adviceCurrent"
                :group-item="groupItem"
                @openDiagnosis="$emit('openDiagnosis')"
            ></discharge-select>
        </div>
        <div class="td" :class="`width-${MPAddTable.remark.width}`">
            <advice-remark
                v-model="advice.remark"
                placeholder="备注"
                :max-length="50"
                :is-need-supplement-history="['今日出院', '转院出院'].includes(adviceCurrent.name)"
                :remark-type="AdviceRemarkTypeEnum.Discharge"
                :tag-types="groupItem.tagTypes"
                :advice-tag-types="tagTypes"
                @change-tags="handleChangeTags"
            ></advice-remark>
        </div>
        <div class="td is-cell-disabled" :class="`width-${MPAddTable.dosageCount.width}`"></div>
        <div class="td" :class="`width-${MPAddTable.executeDepartment.width}`">
            <abc-select
                :show-value="curPatientHospitalInfo.wardName"
                :width="MPAddTable.executeDepartment.width"
                no-icon
                disabled
            ></abc-select>
        </div>
        <div class="td is-cell-disabled" :class="`width-${MPAddTable.startTime.width}`">
            <start-time
                v-model="curStartTime"
                disabled
                :tag-types="groupItem.tagTypes"
                :advice-rule-type="advice.type"
                @enter="enterEvent"
            ></start-time>
        </div>
        <div class="td is-cell-disabled" :class="`width-${MPAddTable.stopTime.width}`"></div>
        <div class="td is-cell-disabled" :class="`width-${MPAddTable.executeTime.width}`"></div>
        <div class="td" :class="`width-${MPAddTable.products.width}`" :style="MPAddTable.products.style">
        </div>
    </div>
</template>

<script>
    import DischargeSelect from '@/views-hospital/medical-prescription/components/discharge/components/discharge-select';
    import {
        MedicalAdviceTypeEnum,
        MedicalAdviceTypeStr,
        medicalPrescriptionOptionsEnum,
        AdviceRemarkTypeEnum,
        AdviceTagEnum,
        AdviceLevelTags,
    } from '@/views-hospital/medical-prescription/utils/constants';
    import StartTime from '@/views-hospital/medical-prescription/components/advice-time/start-time.vue';
    import AdviceName
        from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/advices/advice-name-td/advice-name.vue';
    import AdviceRemark from '@/views-hospital/medical-prescription/components/advice-remark/index.vue';
    import { MPAddTable } from '@/views-hospital/medical-prescription/model/mp-add-table.js';
    import { MedicalPrescriptionService } from '@/views-hospital/medical-prescription/model/index.js';
    import { GoodsTypeIdEnum } from '@abc/constants';
    import { mapGetters } from 'vuex';
    import MedicalPrescriptionAPI from 'api/hospital/medical-prescription';
    import { AdviceRuleType } from '@/views-hospital/medical-prescription/utils/constants';
    import { SupplementAdviceConfirm } from '@/views-hospital/medical-prescription/components/supplement-advice-confirm/supplement-advice-confirm.js';
    import { parseTime } from 'utils/index.js';
    export default {
        name: 'DischargeAdvice',
        components: {
            DischargeSelect,
            StartTime,
            AdviceName,
            AdviceRemark,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            advice: {
                type: Object,
            },
            startTime: String,
            type: [String, Number],
            stopTime: String,
            disabled: Boolean,
            patient: Object,
            patientOrderId: {
                type: String,
                default: '',
            },
            groupItem: Object,
            tagTypes: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                MPAddTable,
                AdviceTagEnum,
                AdviceRemarkTypeEnum,
                medicalPrescriptionOptionsEnum,
                typeList: [
                    {
                        value: MedicalAdviceTypeEnum.ONE_TIME,
                        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.ONE_TIME],
                    },
                ],
                reason: '',
                isShowDischargeHospitalDialog: false,
                curKeyword: '',
                isSearching: false,
            };
        },
        computed: {
            ...mapGetters(['currentClinic']),
            curPatientHospitalInfo() {
                return this.$abcPage.$store.curPatientHospitalInfo || {};
            },
            curType: {
                get() {
                    return this.type;
                },
                set(v) {
                    this.$emit('update:type', v);
                },
            },
            // 是否是转院
            isTransfer() {
                return this.adviceCurrent?.type === AdviceRuleType?.TRANSFER_WITH_MEDICINE;
            },
            placeholder() {
                return `请选择${this.isTransfer ? '转院' : '出院'}原因`;
            },
            jsonTypeWithCustomTypeList() {
                return [
                    {
                        typeId: GoodsTypeIdEnum.TRANS,
                    },
                ];
            },
            groupItemHandle: {
                set(val) {
                    this.$emit('update:groupItem', val);
                },
                get() {
                    return this.groupItem;
                },
            },
            isAllowOpenAdviceSupplement() {
                return !!this.groupItemHandle?.tagTypes?.filter((item) => {
                    return item === AdviceTagEnum.SUPPLEMENT;
                }).length;
            },
            dischargeType: {
                get() {
                    return this.type;
                },
                set(v) {
                    this.$emit('update:type', v);
                },
            },
            adviceCurrent: {
                get() {
                    return this.advice;
                },
                set(v) {
                    this.$emit('update:advice', v);
                },
            },
            curStartTime: {
                get() {
                    return this.startTime;
                },
                set(v) {
                    this.$emit('update:startTime', v);
                },
            },
            curStopTime: {
                get() {
                    return this.stopTime;
                },
                set(v) {
                    this.$emit('update:stopTime', v);
                },
            },
        },
        methods: {
            handleChangeTags(tags, isNeedConfirm = false) {
                if (tags.includes(AdviceTagEnum.SUPPLEMENT) && isNeedConfirm) {
                    new SupplementAdviceConfirm({
                        startTime: this.curStartTime,
                        stopTime: this.curStopTime,
                        adviceRemark: this.advice.remark,
                        adviceType: this.curType,
                        adviceRuleType: this.advice.type,
                        onConfirm: ({
                            startTime, stopTime, remark,
                        }) => {
                            this.curStartTime = startTime;
                            this.adviceCurrent.dischargeHospitalTime = startTime;
                            this.adviceCurrent.stopLongAdviceTime = startTime;
                            this.curStopTime = stopTime;
                            this.advice.remark = remark;
                        },
                        onCancel: () => {
                            this.groupItem.tagTypes = this.groupItem.tagTypes.filter((item) => item !== AdviceTagEnum.SUPPLEMENT);
                        },
                    }).generateDialog({
                        parent: this,
                    });
                }

                if (this.groupItem.tagTypes.includes(AdviceTagEnum.SUPPLEMENT) && !tags.includes(AdviceTagEnum.SUPPLEMENT)) {
                    this.curStopTime = '';
                    this.curStartTime = parseTime(new Date(), 'y-m-d h:i', true);
                    this.adviceCurrent.dischargeHospitalTime = parseTime(new Date(), 'y-m-d h:i', true);
                    this.adviceCurrent.stopLongAdviceTime = parseTime(new Date(), 'y-m-d h:i', true);
                }

                // 分离组级别和医嘱级别的tag
                const groupLevelTags = tags.filter((tag) => !AdviceLevelTags.includes(tag));
                const adviceLevelTags = tags.filter((tag) => AdviceLevelTags.includes(tag));

                // 更新组级别的tag
                this.groupItem.tagTypes = groupLevelTags;

                // 更新当前医嘱的tag
                this.$emit('update:tagTypes', adviceLevelTags);
            },
            async fetch() {
                try {
                    this.list = await MedicalPrescriptionAPI.getDiagnosisList({
                        patientOrderId: this.patientOrderId,
                    });
                } catch (e) {
                    console.log(e);
                }
            },
            enterEvent(e) {
                this.$emit('enter', e);
            },
            async handleChangeMedicine(goods) {
                const adviceItem = MedicalPrescriptionService.getAdviceGoodsItem(goods);
                this.advice.items = [adviceItem];
                this.advice.name = goods.name;
                this.advice.type = MedicalPrescriptionService.getAdviceRuleType(goods);
                // 初始化医嘱的数据
                this.advice.singleDosageCount = goods.packageUnit;
                this.advice.singleDosageUnit = goods.packageUnit;
                this.advice.dosageUnit = goods.packageUnit;
                this.advice.dosageCount = '';
                this.advice.dosageUnit = '';
                this.advice.isDismounting = 0;
                this.isSearching = false;
            },
        },
    };
</script>

