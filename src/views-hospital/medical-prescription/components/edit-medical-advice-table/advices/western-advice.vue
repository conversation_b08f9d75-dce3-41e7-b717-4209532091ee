<template>
    <div class="edit-medical-advice-item edit-western-medicine-advice-item  tr">
        <div class="td table-sticky" :class="`width-${MPAddTable.checkbox.width}`">
            <div class="td-cell">
                <abc-checkbox v-model="advice.isChecked">
                </abc-checkbox>
            </div>
        </div>
        <div class="td table-sticky" :class="`width-${MPAddTable.type.width}`" :style="{ left: `${MPAddTable.checkbox.width}px` }">
            <abc-select
                v-model="curType"
                no-icon
                :width="MPAddTable.type.width"
                :inner-width="100"
            >
                <abc-option
                    v-for="item in typeList"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                >
                </abc-option>
            </abc-select>
        </div>
        <advice-name
            :advice="advice"
            :patient="patient"
            :advice-type="curType"
            :json-type-with-custom-type-list="jsonTypeWithCustomTypeList"
            :dispense-type="curDispenseType"
            show-spec
            show-medical-fee-grade
            :validate-stock="!advice.items[0]?.chargeFlag"
            :is-group-start="isGroupStart"
            :group-count="groupCount"
            show-antimicrobial
            @select="handleChangeMedicine"
        ></advice-name>
        <div class="td" :class="`width-${MPAddTable.usage.width}`">
            <select-group
                v-model="curUsage"
                type="usages"
                placeholder="用法"
                @change="handeUsageChange"
            ></select-group>
        </div>
        <div class="td" :class="`width-${MPAddTable.freq.width}`">
            <div ref="freq-popper" style="position: relative;">
                <freq
                    ref="freq"
                    :value="curFreq"
                    :advice-group-type="curType"
                    :width="58"
                    :advice-rule-type="advice.type"
                    @enter="enterEvent"
                    @change="handleChangeFreqInfo"
                ></freq>
                <div
                    ref="custom-freq-popper"
                    style="position: absolute; top: 0; left: 0; z-index: 2000;"
                >
                    <custom-freq-dialog
                        v-if="showCustomFreq"
                        :custom-type="customFreq"
                        @close="closeCustomFreqDialog"
                        @confirm="confirmCustomFreqHandle"
                    ></custom-freq-dialog>
                </div>
            </div>
        </div>
        <div class="td" :class="`width-${MPAddTable.singleDosageCount.width}`" :style="MPAddTable.singleDosageCount.style">
            <single-dosage
                required
                :medicine="advice.items[0]"
                placeholder="单次"
                :input-width="50"
                :unit-width="36"
                :single-dosage-count.sync="advice.singleDosageCount"
                :single-dosage-unit.sync="advice.singleDosageUnit"
                :single-dosage-unit-type.sync="advice.singleDosageUnitType"
                @change="handleSingleCountChange"
                @single-unit-change="handleSingleUnitChange"
                @enter="enterEvent"
            >
            </single-dosage>
        </div>
        <div
            class="td"
            :class="`width-${MPAddTable.days.width} ${!isDischarge ? 'is-cell-disabled' : ''}`"
        >
            <abc-form-item>
                <abc-input
                    v-model="curDays"
                    v-abc-focus-selected
                    :disabled="!isDischarge"
                    :width="32"
                    :input-custom-style="{ padding: '3px' }"
                    margin="0"
                    :max-length="4"
                    type="number"
                    class="count-center"
                    @enter="enterEvent"
                >
                </abc-input>
            </abc-form-item>
            <div class="input-append-unit">
                天
            </div>
        </div>
        <div class="td " :class="`width-${MPAddTable.ast.width}`">
            <ast-td
                :width="MPAddTable.ast.width"
                class="table-td ast"
                :item="advice"
            ></ast-td>
        </div>
        <div class="td" :class="`width-${MPAddTable.remark.width}`">
            <ivgtt v-if="showIvgtt" v-model="groupItem"></ivgtt>
            <goods-remark
                ref="medicineRemarks"
                v-model="advice.remark"
                placeholder="备注"
                max-length="50"
                placement="bottom-start"
                :readonly="false"
                support-tag
                :pr-form-item="advice.items[0]"
                :tabindex="-1"
                :ward-area-id="patientWardId"
                :tag-types="groupItem.tagTypes"
                :advice-tag-types="tagTypes"
                show-medicine-remark-options
                show-medicine-source-options
                show-no-charge
                :show-ivgtt="showIvgtt"
                :show-psychotropic-narcotic-type="true"
                @changeExtend="handleChangeSource"
                @change-tags="handleChangeTags"
                @enter="enterEvent"
            ></goods-remark>
        </div>
        <div class="td" :class="`width-${MPAddTable.dosageCount.width}`">
            <abc-form-item v-if="isLongTimeMedical" required>
                <abc-select
                    v-model="curDispenseType"
                    :width="36"
                    no-icon
                    :inner-width="326"
                >
                    <abc-option :value="AdviceDispenseType.SINGLE" label="单">
                        <div>
                            <span
                                :style="{
                                    color: $store.state.theme.style.T3, 'line-height': '22px'
                                }"
                            >单次:</span>
                            <span>每次医嘱执行单独发药</span>
                        </div>
                    </abc-option>
                    <abc-option :value="AdviceDispenseType.ALL" label="全">
                        <div>
                            <span
                                :style="{
                                    color: $store.state.theme.style.T3, 'line-height': '22px'
                                }"
                            >全部:</span>
                            <span>首次执行发全部药品，后续不再单独领药</span>
                        </div>
                    </abc-option>
                </abc-select>
            </abc-form-item>
            <dosage
                ref="dosageCount"
                :medicine.sync="advice.items[0]"
                :placeholder="isSingleDispenseType ? '总量' : '数量'"
                required
                :input-width="!isLongTimeMedical ? 82 : 46"
                :unit-width="36"
                :dosage-count.sync="dosageInfo.dosageCount"
                :dosage-unit.sync="dosageInfo.dosageUnit"
                :is-dismounting.sync="dosageInfo.dosageIsDismounting"
                :goods-dismounting="goodsDismounting"
                @changeCount="handleChangeDosageCount"
                @changeUnit="handleChangeDosageCountUnit"
                @enter="enterEvent"
            >
            </dosage>
        </div>
        <div ref="goodsPharmacySelector" class="td" :class="`width-${MPAddTable.executeDepartment.width}`">
            <abc-select
                :show-value="pharmacyName"
                :width="MPAddTable.executeDepartment.width"
                :inner-width="220"
                no-icon
                focus-show-options
                @change="handleChangeSource"
            >
                <abc-option
                    :value="{
                        id: null,
                        content: '自备',
                        sourceType: 1,
                    }"
                    label="自备"
                >
                    <span>
                        自备
                    </span>

                    <abc-text
                        size="mini"
                        theme="gray-light"
                        style="margin-left: auto;"
                    >
                        -
                    </abc-text>
                </abc-option>
                <abc-option
                    v-for="pharmacy in localPharmacyList"
                    :key="pharmacy.no"
                    :value="pharmacy"
                    :label="pharmacy.name"
                >
                    <span>
                        {{ pharmacy.name }}
                    </span>

                    <abc-text
                        v-if="defaultPharmacy && pharmacy.no === defaultPharmacy.no"
                        size="mini"
                        theme="gray-light"
                        style="margin-left: 4px;"
                    >
                        默认
                    </abc-text>

                    <abc-text
                        size="mini"
                        theme="gray-light"
                        style=" max-width: 60px; margin-left: auto;"
                        class="ellipsis"
                    >
                        {{ pharmacy.pharmacyTip }}
                    </abc-text>
                </abc-option>
            </abc-select>
        </div>
        <div class="td" :class="`width-${MPAddTable.startTime.width}`">
            <start-time
                v-model="curStartTime"
                required
                :advice-rule-type="advice.type"
                :tag-types="groupItem.tagTypes"
                :stop-time="curStopTime"
                @change="handleChangeStartTime"
                @enter="enterEvent"
            ></start-time>
        </div>
        <div class="td" :class="`width-${MPAddTable.stopTime.width} ${!isLongTimeMedical ? 'is-cell-disabled' : ''}`">
            <stop-time
                v-if="isLongTimeMedical"
                v-model="curStopTime"
                :start-time="curStartTime"
                :required="false"
                @change="handleChangeStopTime"
            ></stop-time>
        </div>
        <div class="td" :class="`width-${MPAddTable.executeTime.width} ${!isLongTimeMedical ? 'is-cell-disabled' : ''}`" @click.stop="handleCheckFreq">
            <execute-time-select
                v-if="showExecuteTimeSelect"
                :freq-info="curFreqInfo"
                @confirm="handleConfirmExecuteItem"
            ></execute-time-select>
        </div>
        <div
            class="td"
            :class="`width-${MPAddTable.products.width}`"
            :style="MPAddTable.products.style"
        >
            <relate-product
                :advice-type="curType"
                :advice="advice"
                :usage="curUsage"
                is-related
                is-medicine
                @change-pay-type="val => $set(advice, 'goodsIdMedicalInsurancePayTypes', val)"
            ></relate-product>
        </div>
    </div>
</template>

<script>
    import {
        AdviceDispenseType,
        AdviceTagEnum,
        AdviceLevelTags,
        MedicalAdviceTypeEnum,
        MedicalAdviceTypeStr,
        ST_FREQ,
    } from '@/views-hospital/medical-prescription/utils/constants.js';

    import SelectGroup from '@/views/layout/select-group/index.vue';
    import Freq from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/components/freq.vue';
    import SingleDosage from '../components/single-dosage.vue';
    import Dosage from '../components/dosage.vue';
    import AstTd from '../components/ast-td.vue';
    import GoodsRemark from '../../goods-remark/index.vue';
    import Ivgtt from 'views/layout/prescription/common/ivgtt.vue';
    import ExecuteTimeSelect from '@/views-hospital/medical-prescription/components/execute-time-select/index.vue';
    import StartTime from '@/views-hospital/medical-prescription/components/advice-time/start-time.vue';
    import StopTime from '@/views-hospital/medical-prescription/components/advice-time/stop-time.vue';
    import AdviceName from './advice-name-td/advice-name.vue';
    import CustomFreqDialog from 'views/layout/prescription/common/custom-freq-dialog.vue';
    import RelateProduct
        from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/advices/related-product/relate-product.vue';
    import {
        SupplementAdviceConfirm,
    } from '@/views-hospital/medical-prescription/components/supplement-advice-confirm/supplement-advice-confirm.js';

    import CdssAPI from 'api/cdss/index.js';
    import MedicalPrescriptionAPI from 'api/hospital/medical-prescription/index.js';

    import { mapGetters } from 'vuex';
    import { parseTime } from '@abc/utils-date';
    import {
        calcDaysByTimeDifference, calcStopTime,
        getFreqInfoByFirst,
        getSingleDosageUnitType,
        getSuggestCountAndUnit,
    } from '@/views-hospital/medical-prescription/utils/format-advice.js';
    import { OutpatientChargeTypeEnum } from 'views/outpatient/constants.js';
    import { MedicalPrescriptionService } from '@/views-hospital/medical-prescription/model/index.js';
    import { MPAddTable } from '@/views-hospital/medical-prescription/model/mp-add-table.js';
    import {
        GoodsSubTypeEnum, GoodsTypeEnum, GoodsTypeIdEnum,
    } from '@abc/constants';
    import { getDefaultPharmacy } from 'views/common/pharmacy.js';
    import { resetStockByPharmacyNo } from 'views/layout/prescription/utils';

    import Popper from 'utils/vue-popper';
    import { isNull } from '@/utils';
    import { isAllowAddByAntimicrobialDrugManagement } from 'views/outpatient/utils';
    import AntimicrobialDrugManagementModal from 'views/outpatient/common/antimicrobial-drug-limit-modal';

    export default {
        name: 'WesternAdvice',
        components: {
            SelectGroup,
            Freq,
            SingleDosage,
            Dosage,
            AstTd,
            GoodsRemark,
            Ivgtt,
            ExecuteTimeSelect,
            StartTime,
            StopTime,
            AdviceName,
            CustomFreqDialog,
            RelateProduct,
        },
        mixins: [Popper],
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            advice: {
                type: Object,
            },
            type: [String, Number],
            usage: String,
            freq: {
                type: String,
            },
            freqInfo: [Object, null],
            days: [Number, String],
            startTime: String,
            stopTime: String,
            endTime: String,
            dispenseType: Number,
            patient: Object,
            groupItem: Object,
            isGroupStart: {
                type: Boolean,
                default: false,
            },
            groupCount: {
                type: Number,
                default: 1,
            },
            tagTypes: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                MPAddTable,
                MedicalAdviceTypeEnum,
                AdviceDispenseType,
                typeList: [
                    {
                        value: MedicalAdviceTypeEnum.ONE_TIME,
                        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.ONE_TIME],
                    },
                    {
                        value: MedicalAdviceTypeEnum.LONG_TIME,
                        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.LONG_TIME],
                    },
                    {
                        value: MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE,
                        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE],
                    },
                ],
                isSearching: false,
                curKeyword: '',
                pickerOptions: {
                    disabledDate(date) {
                        const today = new Date();
                        today.setHours(0, 0, 0, 0);
                        return date < today;
                    },
                },
                lastMedicine: null,
                showCustomFreq: false,
                customFreq: '',
                dosageInfo: {
                    dosageCount: '',
                    dosageUnit: '',
                    dosageIsDismounting: 0,
                },
            };
        },
        computed: {
            ...mapGetters('executeTime', ['allExecuteTimeList']),
            ...mapGetters([
                'currentClinic',
                'pharmacyRuleList',
                'westernMedicineConfig',
                'enableLocalPharmacyList',
                'userInfo',
            ]),
            doctorId() {
                return this.userInfo.id || '';
            },
            westernInfusionUsages() {
                const { usage } = this.westernMedicineConfig;
                return (usage || []).filter((it) => it.type === 2).map((it) => it.name);
            },

            curSelectProductInfo() {
                return this.advice?.items?.[0]?.productInfo || null;
            },

            localPharmacyList() {
                return (this.enableLocalPharmacyList || []).map((it) => {
                    if (!this.curSelectProductInfo) {
                        return {
                            ...it,
                            content: it.name,
                            sourceType: 1,
                        };
                    }

                    const {
                        packageUnit, pieceUnit, pharmacyGoodsStockList,
                    } = this.curSelectProductInfo;

                    if (!pharmacyGoodsStockList) {
                        return {
                            ...it,
                            content: it.name,
                            sourceType: 1,
                        };
                    }

                    const stock = pharmacyGoodsStockList.find((x) => x.pharmacyNo === it.no);
                    if (!stock) {
                        return {
                            ...it,
                            content: it.name,
                            sourceType: 1,
                            pharmacyTip: `0${packageUnit || pieceUnit}`,
                        };
                    }

                    let str = '';
                    if (packageUnit) {
                        str += `${stock.stockPackageCount || 0}${packageUnit}`;
                    }
                    if (pieceUnit) {
                        str += `${stock.stockPieceCount || 0}${pieceUnit}`;
                    }

                    return {
                        ...it,
                        content: it.name,
                        sourceType: 1,
                        pharmacyTip: str,
                    };
                });
            },
            pharmacyName() {
                const { pharmacyNo } = this.advice;
                const pharmacyItem = this.enableLocalPharmacyList.find((it) => it.no === pharmacyNo);
                return pharmacyItem && pharmacyItem.name ? pharmacyItem.name : '自备';
            },
            patientWardId() {
                return this.$abcPage.$store.currentPatientWardId;
            },
            defaultPharmacy() {
                return getDefaultPharmacy(this.pharmacyRuleList, {
                    wardAreaId: this.patientWardId,
                    goodsInfo: this.advice?.items[0],
                });
            },
            executeTimeList() {
                return this.allExecuteTimeList || [];
            },
            curUsage: {
                get() {
                    return this.usage;
                },
                set(v) {
                    this.$emit('update:usage', v);
                },
            },
            curFreq: {
                get() {
                    return this.freq;
                },
                set(v) {
                    this.$emit('update:freq', v);
                },
            },
            curDays: {
                get() {
                    return this.days;
                },
                set(v) {
                    this.$emit('update:days', v);
                },
            },
            curStartTime: {
                get() {
                    return this.startTime;
                },
                set(v) {
                    this.$emit('update:startTime', v);
                },
            },
            curStopTime: {
                get() {
                    return this.stopTime;
                },
                set(v) {
                    this.$emit('update:stopTime', v);
                },
            },
            curType: {
                get() {
                    return this.type;
                },
                set(v) {
                    this.$emit('update:type', v);
                },
            },
            showIvgtt() {
                return this.curUsage === '静脉滴注';
            },
            curFreqInfo: {
                get() {
                    return this.freqInfo;
                },
                set(v) {
                    this.$emit('update:freqInfo', v);
                },
            },
            curDispenseType: {
                get() {
                    return this.dispenseType;
                },
                set(v) {
                    this.$emit('update:dispenseType', v);
                },
            },
            patientInfo() {
                return this.patient;
            },

            isLongTimeMedical() {
                return this.curType === MedicalAdviceTypeEnum.LONG_TIME;
            },
            isOneTimeMedical() {
                return this.curType === MedicalAdviceTypeEnum.ONE_TIME;
            },
            showExecuteTimeSelect() {
                return this.isLongTimeMedical && this.curFreq;
            },
            isDischarge() {
                return this.curType === MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE || this.curType === MedicalAdviceTypeEnum.LONG_TIME;
            },
            jsonTypeWithCustomTypeList() {
                return [
                    {
                        typeId: GoodsTypeIdEnum.MEDICINE_WESTERN,
                    },
                    {
                        typeId: GoodsTypeIdEnum.MEDICINE_CHINESE_PATENT,
                    },
                ];
            },
            // 是否是单次发药模式
            isSingleDispenseType() {
                return this.curDispenseType === AdviceDispenseType.SINGLE;
            },
            // 该药品 goods 是否可以拆零销售
            goodsDismounting() {
                return this.advice.items?.[0]?.dismounting ?? 1;
            },
        },
        watch: {
            // 分组的医嘱类型变更
            curType(val) {
                this.handleChangeAdviceType(val);
            },
            // 分组的发药类型变更
            curDispenseType() {
                this.handleCalcDosageCount();
            },
            // 分组的频率变更
            curFreq(val) {
                this.curFreqInfo = getFreqInfoByFirst(this.executeTimeList, val, true, this.curStartTime);
                this.handleCalcDosageCount();
            },
            // 分组的天数变更
            curDays(val) {
                this.handleChangeDays(val);
            },
            // 分组的首次执行时间变更
            curFreqInfo: {
                handler(val) {
                    // 长期医嘱修改首日/首周执行次数需要重新计算总量
                    if (val && this.isLongTimeMedical) {
                        this.handleCalcDosageCount();
                    }
                },
                deep: true,
            },
        },
        created() {
            this.$store.dispatch('initDoctorWesternPRRemarks');
        },
        mounted() {
            this.referenceElm = this.$refs['freq-popper'];
            this.popperElm = this.$refs['custom-freq-popper'];
            this.createPopper();
            // 转换数据，绑定用户输入
            Object.assign(this.dosageInfo, {
                dosageCount: this.isSingleDispenseType ? this.advice.singleDispenseCount : this.advice.dosageCount,
                dosageUnit: this.isSingleDispenseType ? this.advice.singleDispenseUnit : this.advice.dosageUnit,
                dosageIsDismounting: this.isSingleDispenseType ? this.advice.singleDispenseIsDismounting : this.advice.dosageIsDismounting,
            });
        },
        methods: {
            handleChangeDispenseType(val) {
                // 全部发药模式 清空总量
                if (val === AdviceDispenseType.ALL) {
                    this.dosageInfo.dosageCount = '';
                }
                this.initAdviceDispenseInfo();
            },
            /**
             * @desc: 转换到后台接受的参数
             * @author: ff
             * @date: 2024/1/17
             * @description:
             * @params
             * @return
             */
            initAdviceDispenseInfo() {
                // 拆零方式保持一致
                this.advice.singleDispenseIsDismounting = this.dosageInfo.dosageIsDismounting;
                this.advice.dosageIsDismounting = this.dosageInfo.dosageIsDismounting;

                if (this.isSingleDispenseType) {
                    this.advice.singleDispenseCount = this.dosageInfo.dosageCount;
                    this.advice.singleDispenseUnit = this.dosageInfo.dosageUnit;
                    this.advice.dosageCount = null;
                    this.advice.dosageUnit = null;
                } else {
                    this.advice.singleDispenseCount = null;
                    this.advice.singleDispenseUnit = null;
                    this.advice.dosageCount = this.dosageInfo.dosageCount;
                    this.advice.dosageUnit = this.dosageInfo.dosageUnit;
                }
            },
            enterEvent(e, $el) {
                if ($el &&
                    $el.className.indexOf('abc-select-wrapper') > -1 &&
                    ['qnd', 'qnw'].indexOf(this.curFreq) > -1) {
                    return false;
                }
                this.$emit('enter', e);
                // 找到所有的非disabled的input输入框
                const inputs = $('.edit-western-medicine-advice-item .is-required').find('.abc-input__inner').not(':disabled');
                const targetIndex = inputs.index(e.target);
                let nextInput = inputs[ targetIndex + 1 ];
                if (nextInput?.tabIndex === -1) {
                    nextInput = inputs[targetIndex + 2];
                }
                // 碰到连续tabindex===-1的情况，再加一次
                if (nextInput?.tabIndex === -1) {
                    nextInput = inputs[targetIndex + 3];
                }
                if (nextInput) {
                    this.$nextTick(() => {
                        nextInput.select && nextInput.select();
                        nextInput.focus && nextInput.focus();
                        // magic code
                        this._timer = setTimeout(() => {
                            nextInput.selectionStart = 0;
                            nextInput.selectionEnd = nextInput.value ? nextInput.value.length : 0;
                            nextInput.select && nextInput.select();
                            nextInput.focus && nextInput.focus();
                        }, 50);
                    });
                } else {
                    $('.advice-product-autocomplete .abc-input__inner')?.eq(0)?.focus();
                }
            },
            /**
             * @desc: 非 长期医嘱 只能全部发药模式
             * @author: ff
             * @date: 2024/1/18
             * @description:
             */
            async handleChangeAdviceType(val) {
                if (val !== MedicalAdviceTypeEnum.LONG_TIME) {
                    this.curDispenseType = AdviceDispenseType.ALL;
                }
                this.curStopTime = '';
                if (val === MedicalAdviceTypeEnum.ONE_TIME) {
                    // 临时医嘱，频率 ST, 计算发药总量
                    this.curFreq = ST_FREQ;
                    this.curDays = 1;
                } else {
                    this.curFreq = '';
                    this.curDays = '';
                }
            },
            handleChangeFreqInfo(val) {
                if (['qnd', 'qnw', 'nid'].includes(val)) {
                    this.customFreq = val;
                    this.$refs.freq.$refs.freqSelector.showPoper = false;
                    this.showCustomFreq = true;
                } else {
                    this.customFreq = '';
                    this.curFreq = val;
                }
            },
            closeCustomFreqDialog() {
                this.showCustomFreq = false;
            },
            confirmCustomFreqHandle(val) {
                if (val) {
                    this.curFreq = val;
                }
            },
            handleSingleCountChange() {
                this.handleCalcDosageCount();
                if (this.isLongTimeMedical && this.curDispenseType === AdviceDispenseType.SINGLE) {
                    this.$Toast({
                        message: '发药数量已发生变化',
                        type: 'error',
                        duration: 2000,
                        referenceEl: this.$refs.dosageCount.$el,
                    });
                }
            },
            handleSingleUnitChange(oldVal) {
                if (this.isOneTimeMedical) return;
                const {
                    packageUnit,
                    pieceNum,
                    pieceUnit,
                    medicineDosageUnit, // 剂量单位 g
                    medicineDosageNum, // 剂量单位总量， 2 g/片
                    componentContentUnit, // 成分单位
                    componentContentNum,
                } = this.advice.items[0];
                if (this.advice.singleDosageUnit === pieceUnit) {
                    if (oldVal === medicineDosageUnit) {
                        this.advice.singleDosageCount = Math.ceil(this.advice.singleDosageCount / medicineDosageNum);
                    } else if (oldVal === componentContentUnit) {
                        this.advice.singleDosageCount = Math.ceil(this.advice.singleDosageCount / componentContentNum);
                    } else if (oldVal === packageUnit) {
                        this.advice.singleDosageCount = Math.ceil(this.advice.singleDosageCount * pieceNum);
                    }
                } else if (this.advice.singleDosageUnit === componentContentUnit) {
                    if (oldVal === pieceUnit) {
                        this.advice.singleDosageCount = Math.ceil(this.advice.singleDosageCount * componentContentNum);
                    } else if (oldVal === medicineDosageUnit) {
                        this.advice.singleDosageCount = Math.ceil(this.advice.singleDosageCount / medicineDosageNum * componentContentNum);
                    } else if (oldVal === packageUnit) {
                        this.advice.singleDosageCount = Math.ceil(this.advice.singleDosageCount * pieceNum * componentContentNum);
                    }
                } else if (this.advice.singleDosageUnit === medicineDosageUnit) {
                    if (oldVal === pieceUnit) {
                        this.advice.singleDosageCount = Math.ceil(this.advice.singleDosageCount * medicineDosageNum);
                    } else if (oldVal === componentContentUnit) {
                        this.advice.singleDosageCount = Math.ceil(this.advice.singleDosageCount / componentContentNum * medicineDosageNum);
                    } else if (oldVal === packageUnit) {
                        this.advice.singleDosageCount = Math.ceil(this.advice.singleDosageCount * pieceNum * medicineDosageNum);
                    }
                } else if (this.advice.singleDosageUnit === packageUnit) {
                    if (oldVal === pieceUnit) {
                        this.advice.singleDosageCount = Math.ceil(this.advice.singleDosageCount / pieceNum);
                    } else if (oldVal === componentContentUnit) {
                        this.advice.singleDosageCount = Math.ceil(this.advice.singleDosageCount / componentContentNum / pieceNum);
                    } else if (oldVal === medicineDosageUnit) {
                        this.advice.singleDosageCount = Math.ceil(this.advice.singleDosageCount / medicineDosageNum / pieceNum);
                    }
                }
                this.handleCalcDosageCount();
            },
            /**
             * @desc: 计算 药品 发药量
             * @author: ff
             * @date: 2024/1/18
             * @description:
             * @params
             * @return
             */
            calMedicineDispense() {
                // 全部发药
                if (this.curDispenseType === AdviceDispenseType.ALL) {
                    if (!this.curFreq) return;
                    if (!this.curFreqInfo) return;
                    if (!this.curDays && (!this.curStartTime || !this.curStopTime)) return;
                    const {
                        unit, unitCount, dismounting,
                    } = MedicalPrescriptionService.calMedicineCountByDispensingAll({
                        advice: this.advice,
                        startTime: this.curStartTime,
                        stopTime: this.curStopTime,
                        days: this.curDays,
                        freq: this.curFreq,
                        freqInfo: this.curFreqInfo,
                        usage: this.curUsage,
                        isDischargeAdvice: false,
                    });
                    Object.assign(this.dosageInfo, {
                        dosageUnit: unit,
                        dosageCount: unitCount,
                        dosageIsDismounting: dismounting,
                    });
                } else {
                    // 单次发药
                    const {
                        unit, unitCount, dismounting,
                    } = MedicalPrescriptionService.calMedicineCount(this.advice, this.curUsage);
                    Object.assign(this.dosageInfo, {
                        dosageUnit: unit,
                        dosageCount: unitCount,
                        dosageIsDismounting: dismounting,
                    });
                }
            },
            /**
             * 计算出院带药类型的发药量
             * Tips: 和【长期医嘱-全部发药】的计算方式一致
             */
            calcMedicineDispenseByDischarge() {
                if (!this.curFreq) return;
                if (!this.curFreqInfo) return;
                if (!this.curDays && (!this.curStartTime || !this.curStopTime)) return;
                const {
                    unit, unitCount, dismounting,
                } = MedicalPrescriptionService.calMedicineCountByDispensingAll({
                    advice: this.advice,
                    startTime: this.curStartTime,
                    stopTime: this.curStopTime,
                    days: this.curDays,
                    freq: this.curFreq,
                    freqInfo: this.curFreqInfo,
                    usage: this.curUsage,
                    isDischargeAdvice: true,
                });
                Object.assign(this.dosageInfo, {
                    dosageUnit: unit,
                    dosageCount: unitCount,
                    dosageIsDismounting: dismounting,
                });
            },
            /**
             * 计算天数
             */
            async handleCalcDays() {
                // 如果是临时医嘱, 则不需要反算天数
                if (this.isOneTimeMedical) return;
                // 如果是长期医嘱单次发药, 则不需要反算天数
                if (this.isLongTimeMedical && this.curDispenseType === AdviceDispenseType.SINGLE) return;
                // 如果已经填了天数, 则不再反算
                if (this.curDays) return;
                await this.$nextTick();
                const resDays = MedicalPrescriptionService.calcMedicineDays({
                    advice: this.advice,
                    dosageCount: this.dosageInfo.dosageCount,
                    dosageUnit: this.dosageInfo.dosageUnit,
                    freq: this.curFreq,
                    freqInfo: this.curFreqInfo,
                });
                if (resDays) {
                    // 总量改变计算出天数改变,不再反算总量
                    this._daysIsUpdatedByDosageCount = true;
                    this.curDays = resDays;
                }
            },
            handleChangeDosageCount() {
                this.initAdviceDispenseInfo();
                this.handleCalcDays();
            },
            /**
             * @desc 计算药品总量
             * <AUTHOR>
             * @date 2023-02-13 10:24:15
             */
            async handleCalcDosageCount() {
                await this.$nextTick();
                if (!this.curStartTime) return;
                if (!this.advice.singleDosageCount) return;

                // 长期医嘱
                if (this.isLongTimeMedical) {
                    this.calMedicineDispense();
                    this.initAdviceDispenseInfo();
                    return;
                }

                // 临时医嘱
                if (this.curFreq === ST_FREQ) {
                    const {
                        unit, unitCount, dismounting,
                    } = MedicalPrescriptionService.calMedicineCount(this.advice, this.curUsage);
                    Object.assign(this.dosageInfo, {
                        dosageUnit: unit,
                        dosageCount: unitCount,
                        dosageIsDismounting: dismounting,
                    });
                    this.initAdviceDispenseInfo();
                    return;
                }

                // 出院带药
                /**
                 * 新: 从后台计算调整为前端计算
                 */
                this.calcMedicineDispenseByDischarge();
                this.initAdviceDispenseInfo();
                /**
                 * 旧: 后台计算
                 */
                // 出院带药需要根据频率计算 医嘱执行次数
                // if (!this.curDays) return;
                // if (!this.curFreq) return;
                // const endTime = this.handleCalcEndTime();
                // const res = await this.calcDosageCount({
                //     endTime,
                //     freqInfo: this.curFreqInfo,
                //     freq: this.freq,
                //     startTime: this.curStartTime,
                // });
                // // 医嘱数量乘以单次剂量得到总的剂量
                // this.dosageInfo.dosageCount = (res?.data?.count || 1) * this.advice.singleDosageCount;
            },
            handleCalcEndTime() {
                if (!this.curStartTime) return;
                if (!this.curDays) return;
                let endTime = new Date(this.curStartTime).getTime() + ((this.curDays - 1) * 24 * 60 * 60 * 1000);
                endTime = parseTime(new Date(endTime), 'y-m-d h:i', true);
                return endTime;
            },
            handleCheckFreq() {
                if (this.isLongTimeMedical && !this.curFreq) {
                    this.$Toast({
                        type: 'error',
                        message: '请先填写频率',
                    });
                }
            },
            handleConfirmExecuteItem() {
                // this.$set(this.curFreqInfo, 'firstDayTimings', firstDayTimings);
                // this.$set(this.curFreqInfo, 'firstDayFrequency', firstDayFrequency);
                // 长期医嘱修改首日/首周执行次数需要重新计算总量
                if (this.isLongTimeMedical) {
                    this.$nextTick(() => {
                        this.handleCalcDosageCount();
                    });
                }
            },
            /**
             * @desc 获取药品的最近用法
             * <AUTHOR>
             * @date 2023-02-02 15:17:23
             */
            async fetchMedicineUsages(goods, patient) {
                try {
                    const { data } = await CdssAPI.fetchMedicineUsage({
                        medicineCadn: goods.medicineCadn,
                        goodsId: goods.goodsId,
                        type: goods.type,
                        patientInfo: patient,
                        usageFrom: 10,
                    });
                    return data;
                } catch (e) {
                    console.log('获取推荐用法失败', e);
                }
            },
            /**
             * 判断西药和中成药的抗菌限制是否合规
             * @return {boolean}
             */
            preCheckAntimicrobial(productInfo) {
                if (productInfo.type === GoodsTypeEnum.MEDICINE && [GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine, GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM].includes(productInfo.subType)) {
                    const {
                        antimicrobialDrugManagementData, employeeListByPractice,
                    } = this.$store.getters;
                    const item = {
                        name: productInfo.medicineCadn || productInfo.name,
                        productInfo,
                    };
                    const isSuccess = isAllowAddByAntimicrobialDrugManagement(item, this.doctorId, antimicrobialDrugManagementData, employeeListByPractice);
                    if (!isSuccess) {
                        // eslint-disable-next-line abc/no-timer-id
                        setTimeout(() => {
                            new AntimicrobialDrugManagementModal({
                                list: [item], showViewCheckDoctorDetailBtn: true,
                            }).generateDialogAsync({ parent: this });
                        }, 100);
                        return false;
                    }
                }
                return true;
            },
            async handleChangeMedicine(goods) {
                this.isSearching = false;

                // 判断西药和中成药的抗菌限制是否合规
                if (!this.preCheckAntimicrobial(goods)) return false;

                const adviceItem = MedicalPrescriptionService.getAdviceGoodsItem(goods);
                this.advice.items = [adviceItem];

                this.advice.singleDosageUnit = '';
                const data = await this.fetchMedicineUsages(goods, this.patientInfo);
                if (data) {
                    this.curUsage = data.usage;
                    const {
                        unit, count,
                    } = getSuggestCountAndUnit(goods, data.dosageUnit, data.dosage);
                    this.advice.singleDosageCount = count;
                    this.advice.singleDosageUnit = unit;

                    this.advice.ivgtt = data.ivgtt;
                    this.advice.ivgttUnit = data.ivgttUnit;
                    this.advice.singleDosageUnitType = getSingleDosageUnitType(this.advice.singleDosageUnit, goods);

                    data.freq = this.isOneTimeMedical ? 'st' : (data.freq?.toLocaleLowerCase() === 'st' ? '' : data.freq); // 长期医嘱和出院带药的freq不能为st;
                    this.curFreq = this.curFreqInfo.code ? data.freq : '';
                    this.curFreqInfo = getFreqInfoByFirst(this.executeTimeList, data.freq, true, this.curStartTime);

                    // 计算总量时需要 curFreqInfo 和 curFreq, 需要在这俩更新之后再进行计算
                    this.handleCalcDosageCount();
                }
            },

            /**
             * @desc 计算生成的医嘱数量
             * <AUTHOR>
             * @date 2023-02-10 14:10:21
             */
            async calcDosageCount(data) {
                try {
                    const res = await MedicalPrescriptionAPI.fetchExecuteCount(data);
                    return res;
                } catch (e) {
                    console.warn('医嘱用量计算错误', e);
                }
            },
            handleChangeSource(val) {
                const goods = {};
                if (val.id === null) {
                    goods.pharmacyType = '';
                    goods.pharmacyNo = '';
                    goods.pharmacyName = '';
                    goods.chargeFlag = OutpatientChargeTypeEnum.NO_CHARGE;
                    this.$nextTick(() => {
                        this.$Toast({
                            message: '备注选择【自备】，该药品将不会纳入划价收费',
                            duration: 1500,
                            referenceEl: this.$refs.goodsPharmacySelector,
                        });
                    });
                } else {
                    goods.pharmacyType = val.type;
                    goods.pharmacyNo = val.no;
                    goods.pharmacyName = val.name;
                    goods.chargeFlag = OutpatientChargeTypeEnum.DEFAULT;
                }
                Object.assign(this.advice.items[0], goods);
                this.$nextTick(() => {
                    resetStockByPharmacyNo(this.advice.items[0]);
                });
                // 后台取值是adviceRule上面的pharmacyNo
                this.$set(this.advice, 'pharmacyType', goods.pharmacyType);
                this.$set(this.advice, 'pharmacyNo', goods.pharmacyNo);
                this.$set(this.advice, 'pharmacyName', goods.pharmacyName);
            },
            handleChangeTags(tags, isNeedConfirm = false) {
                if (tags.includes(AdviceTagEnum.SUPPLEMENT) && isNeedConfirm) {
                    new SupplementAdviceConfirm({
                        startTime: this.curStartTime,
                        stopTime: this.curStopTime,
                        adviceRemark: this.advice.remark,
                        adviceType: this.curType,
                        adviceRuleType: this.advice.type,
                        maxLength: 10,
                        onConfirm: ({
                            startTime, stopTime, remark,
                        }) => {
                            if (this.isLongTimeMedical && startTime) {
                                this.curStartTime = startTime;
                                if (stopTime) {
                                    this.curStopTime = stopTime;
                                    this._daysIsUpdatedByStopTime = true;
                                    this.curDays = calcDaysByTimeDifference(startTime, stopTime);
                                }
                            }
                            this.advice.remark = remark;
                        },
                        onCancel: () => {
                            this.groupItem.tagTypes = this.groupItem.tagTypes.filter((item) => item !== AdviceTagEnum.SUPPLEMENT);
                        },
                    }).generateDialog({
                        parent: this,
                    });
                }
                if (this.groupItem.tagTypes.includes(AdviceTagEnum.SUPPLEMENT) && !tags.includes(AdviceTagEnum.SUPPLEMENT)) {
                    this.curStopTime = '';
                    this.curStartTime = parseTime(new Date(), 'y-m-d h:i', true);
                    this.curDays = '';
                }

                // 分离组级别和医嘱级别的tag
                const groupLevelTags = tags.filter((tag) => !AdviceLevelTags.includes(tag));
                const adviceLevelTags = tags.filter((tag) => AdviceLevelTags.includes(tag));

                // 更新组级别的tag
                this.groupItem.tagTypes = groupLevelTags;

                // 更新当前医嘱的tag
                this.$emit('update:tagTypes', adviceLevelTags);

            },

            /**
             * 长期医嘱改变天数，重新计算结束时间和总量
             * 出院带药医嘱改变天数，重新计算总量
             */
            handleChangeDays(days) {
                if (!days) return;
                // 如果是改变了停止时间导致的天数变化，则不重新计算停止时间
                if (this._daysIsUpdatedByStopTime) {
                    this._daysIsUpdatedByStopTime = false;
                } else if (this.isLongTimeMedical && this.curStartTime) {
                    this.curStopTime = calcStopTime(this.curStartTime, days);
                }
                // 如果是改变了总量反算导致的天数变化,则不重新计算总量
                if (this._daysIsUpdatedByDosageCount) {
                    this._daysIsUpdatedByDosageCount = false;
                    return;
                }
                if (!this.isOneTimeMedical) {
                    this.handleCalcDosageCount();
                }
            },

            /**
             * @description: 修改开始时间，重新计算首日执行时间
             * @author: ff
             * @date: 2024/1/24
             */
            handleChangeStartTime(val) {
                this.curFreqInfo = getFreqInfoByFirst(this.executeTimeList, this.curFreq, true, val);
                if (this.isLongTimeMedical && val && this.curStopTime) {
                    this._daysIsUpdatedByStopTime = true;
                    // days 改变会自动重新计算总量, 所以这里不需要再去计算
                    this.curDays = calcDaysByTimeDifference(val, this.curStopTime);
                }
            },

            /**
             * 长期医嘱修改停止时间, 重新计算总量
             */
            handleChangeStopTime(val) {
                if (this.isLongTimeMedical) {
                    if (val && this.curStartTime) {
                        this._daysIsUpdatedByStopTime = true;
                        // days 改变会自动重新计算总量, 所以这里不需要再去计算
                        this.curDays = calcDaysByTimeDifference(this.curStartTime, val);
                    }
                }
            },

            /**
             * 切换总量单位时需进行换算
             */
            handleChangeDosageCountUnit(val, oldUnit) {
                // 临时医嘱不进行单位换算
                if (this.isOneTimeMedical) {
                    this.initAdviceDispenseInfo();
                    return;
                }

                if (!this.advice || !this.advice.items?.[0]) {
                    this.initAdviceDispenseInfo();
                    return;
                }

                // 如果原来没有选择单位, 则不需要进行换算
                if (!oldUnit) {
                    this.initAdviceDispenseInfo();
                    // 反算天数
                    this.handleCalcDays();
                    return;
                }

                const {
                    pieceNum,
                } = this.advice.items[0];

                const dosageCount = parseInt(this.dosageInfo.dosageCount);
                // 已经填写了总剂量才需要换算
                if (!isNaN(dosageCount)) {
                    if (!val) {
                        // 从小单位切换到包装单位(大单位)
                        this.dosageInfo.dosageCount = Math.ceil(dosageCount / pieceNum);
                    } else {
                        // 从大单位切换到小单位
                        this.dosageInfo.dosageCount = Math.ceil(dosageCount * pieceNum);
                    }
                }
                this.initAdviceDispenseInfo();

                // 反算天数
                this.handleCalcDays();
            },
            /**
             * 满足条件: 1.西药和中西成药 2.长期医嘱 3.未填写发药数量
             * 切换用法触发: 1.用法是输注类,发药类型改为单次发药 2.用法是非输注类,发药类型改为全部发药
             */
            handeUsageChange(val) {
                if (this.isLongTimeMedical && isNull(this.dosageInfo?.dosageCount)) {
                    if (this.westernInfusionUsages.includes(val)) {
                        this.curDispenseType = AdviceDispenseType.SINGLE;
                    } else {
                        this.curDispenseType = AdviceDispenseType.ALL;
                    }
                }
                this.$nextTick(() => {
                    this.handleCalcDosageCount();
                });
            },
        },
    };
</script>

