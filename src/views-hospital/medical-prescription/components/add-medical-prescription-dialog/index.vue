<template>
    <abc-dialog
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="开医嘱"
        content-styles="padding:0px; min-height: 628px;max-height:890px;height:auto"
        custom-class="add-medical-prescription_dialog--box"
        :custom-styles="customStyles"
    >
        <div class="add-medical-prescription_dialog--box-body">
            <div class="add-medical-prescription_dialog--box-body-top">
                <div class="add-medical-prescription_dialog--box-body-top-title">
                    <div class="add-medical-prescription_dialog--box-body-top-title-left">
                        <abc-space>
                            <abc-button type="ghost" @click="openChineseDialog">
                                中药
                            </abc-button>
                            <abc-button
                                type="ghost"
                                @click="openExaminationDialog(
                                    CATEGORY_TYPE_ENUM.MEDICINE_WESTERN,
                                    [
                                        CATEGORY_TYPE_ENUM.MEDICINE_WESTERN,
                                        CATEGORY_TYPE_ENUM.MEDICINE_CHINESE_PATENT,
                                    ]
                                )"
                            >
                                中西成药
                            </abc-button>
                            <abc-button
                                type="ghost"
                                @click="openExaminationDialog(
                                    CATEGORY_TYPE_ENUM.INSPECTION,
                                    [
                                        CATEGORY_TYPE_ENUM.INSPECTION,
                                        CATEGORY_TYPE_ENUM.ASSAY,
                                    ]
                                )"
                            >
                                检查申请
                            </abc-button>
                            <abc-space split :size="24">
                                <abc-button
                                    type="ghost"
                                    @click="openExaminationDialog(
                                        CATEGORY_TYPE_ENUM.ASSAY,
                                        [
                                            CATEGORY_TYPE_ENUM.INSPECTION,
                                            CATEGORY_TYPE_ENUM.ASSAY,
                                        ]
                                    )"
                                >
                                    检验申请
                                </abc-button>
                                <abc-button
                                    type="ghost"
                                    icon="a-plus13px"
                                    @click="handleAddHandWrittenAdvice"
                                >
                                    手写嘱托
                                </abc-button>
                            </abc-space>
                        </abc-space>
                    </div>
                    <div class="add-medical-prescription_dialog--box-body-top-title-right">
                        <abc-space split>
                            <abc-space>
                                <abc-button :loading="createLoading" :disabled="isEmptyContent" @click="createMedicalAdvice">
                                    完成
                                </abc-button>
                                <abc-button type="blank" :disabled="disabledSaveModelOperation" @click="saveModel">
                                    保存模板
                                </abc-button>
                                <abc-button type="blank" :disabled="disabledOperation" @click="handleCreateGroup">
                                    分组
                                </abc-button>
                                <abc-button type="blank" :disabled="disabledOperation" @click="handleCancelGroup">
                                    取消分组
                                </abc-button>
                                <abc-button type="danger" :disabled="disabledOperation" @click="handleDelete">
                                    删除
                                </abc-button>
                            </abc-space>

                            <abc-space>
                                <template v-if="$abcSocialSecurity.isOpenSocial && isVerifyInsuranceRestriction">
                                    <abc-button
                                        v-if="isEmptyContent"
                                        variant="ghost"
                                        theme="default"
                                        shape="square"
                                        size="normal"
                                        icon="s-b-shield-chs-line"
                                        :loading="false"
                                        :width="92"
                                        @click="handleOpen"
                                    >
                                        合规
                                    </abc-button>
                                    <abc-button
                                        v-else-if="!insuranceRestrictionResultCount"
                                        variant="ghost"
                                        theme="success"
                                        shape="square"
                                        size="normal"
                                        icon="s-b-shield-chs-line"
                                        :width="92"
                                        :loading="verifyLoading"
                                        @click="handleOpen"
                                    >
                                        通过
                                    </abc-button>
                                    <abc-button
                                        v-else
                                        variant="ghost"
                                        theme="danger"
                                        shape="square"
                                        size="normal"
                                        icon="s-b-shield-chs-line"
                                        :width="92"
                                        :loading="verifyLoading"
                                        :count="insuranceRestrictionResultCount"
                                        @click="handleOpen"
                                    >
                                        风险
                                    </abc-button>
                                </template>
                                <abc-tooltip content="用药助手" placement="bottom-start">
                                    <abc-button
                                        shape="square"
                                        variant="ghost"
                                        theme="primary"
                                        size="normal"
                                        icon="s-n-capsule-line"
                                        @click="viewF1"
                                    >
                                    </abc-button>
                                </abc-tooltip>
                            </abc-space>
                        </abc-space>
                    </div>
                </div>
                <div class="add-medical-prescription_dialog--box-body-top-info">
                    <abc-form ref="medicalPrescriptionForm">
                        <edit-medical-advice-table
                            ref="medicalAdviceTable"
                            :patient="patient"
                            :doctor="doctor"
                            :no="no"
                            :select-quick-item="selectQuickItem"
                            :patient-order-id="patientOrderId"
                            :advice-groups="postData.adviceGroups"
                            :nurse-level-advice="nurseLevelAdvice"
                            :exam-apply-sheet-reqs="postData.examApplySheetReqs"
                            :operate-department-id="operateDepartmentId"
                            :surgery-reqs.sync="postData.surgeryReqs"
                            @openDiagnosis="$emit('openDiagnosis')"
                            @refreshCheckExamApplySheetReqs="checkExamApplySheetReqs"
                        ></edit-medical-advice-table>
                    </abc-form>
                </div>
            </div>
            <div class="add-medical-prescription_dialog--box-body-bottom">
                <div class="add-medical-prescription_dialog--box-body-bottom-title">
                    <div class="add-medical-prescription_dialog--box-body-bottom-title-tabs">
                        <abc-tabs
                            v-model="currentTab"
                            :option="tabs"
                            size="small"
                            @click.native="setMedicalPrescriptionModal(true)"
                        ></abc-tabs>
                    </div>
                    <div class="add-medical-prescription_dialog--box-body-bottom-title-button" @click="setMedicalPrescriptionModal(!isShowMedicalPrescriptionModal)">
                        <template v-if="isShowMedicalPrescriptionModal">
                            <span>收起</span><abc-icon color="#005ED9" icon="dropdown_line_up"></abc-icon>
                        </template>
                        <template v-else>
                            <span>展开</span><abc-icon color="#005ED9" icon="dropdown_line"></abc-icon>
                        </template>
                    </div>
                </div>
                <div v-if="isShowMedicalPrescriptionModal" class="add-medical-prescription_dialog--box-body-bottom-info">
                    <common-medical-prescription
                        v-if="currentTab === 0"
                        v-abc-loading="commonAdviceLoading"
                        :fee-type-name="feeTypeName"
                        :patient="patient"
                        @handleSelect="handleSelect"
                    ></common-medical-prescription>
                    <medical-prescription-modal
                        v-else-if="currentTab === 1"
                        ref="medicalModel"
                        :department-id="departmentId"
                        @useModel="useModel"
                    ></medical-prescription-modal>
                    <medical-prescription-ordered-pro-table
                        v-else-if="currentTab === 2"
                        :patient-id="patient.id"
                        :patient-order-id="patientOrderId"
                        :mp-list="mpList"
                        @copy-medical-prescription="getDetailsByIds"
                    ></medical-prescription-ordered-pro-table>
                </div>
            </div>

            <hospital-medical-insurance-restriction
                v-if="showInsuranceRestriction"
                :hospital-verify-rsp="hospitalVerifyRsp"
                :is-opened="isOpened"
                @close="handleClose"
                @changeShebaoPayMode="changeShebaoPayMode"
                @shebaoRestrictSignature="onShebaoRestrictSignature"
                @shebaoRestrictThreeTimesSignature="shebaoRestrictThreeTimesSignature"
            >
            </hospital-medical-insurance-restriction>
        </div>
        <goods-select-dialog
            v-if="isShowExaminationDialog"
            v-model="isShowExaminationDialog"
            :scene-type="SearchSceneTypeEnum.hospital"
            :ward-area-id="patientWardId"
            :default-category-key="currentTabChoice"
            :category-range="categoryRange"
            :fee-type-name="feeTypeName"
            :patient="patient"
            @onSelectGoods="batchSelect"
        ></goods-select-dialog>
        <chinese-medical-dialog
            v-if="isShowChineseDialog"
            v-model="isShowChineseDialog"
            :department-id="departmentId"
            :patient="patient"
            :group-item="curChineseGroupItem"
            @confirm="addChineseMedical"
        ></chinese-medical-dialog>
        <add-medical-prescription-modal-dialog
            v-if="isShowAddMedicalPrescriptionModalDialog"
            v-model="isShowAddMedicalPrescriptionModalDialog"
            :department-id="departmentId"
            :advice-groups="modelAdviceGroups"
            @createModelMenu="createModelMenu"
            @refreshModel="refreshModel"
        ></add-medical-prescription-modal-dialog>
        <add-medial-diagnosis-dialog
            v-if="isShowDiagnosisDialog"
            v-model="isShowDiagnosisDialog"
            :patient="patient"
            :patient-order-id="patientOrderId"
            :existed-diagnosis-list="diagnosisTableData"
            :doctor="doctor"
            :default-diagnosis-type="defaultDiagnosisType"
        ></add-medial-diagnosis-dialog>
    </abc-dialog>
</template>

<script>
    import CommonMedicalPrescription from '@/views-hospital/medical-prescription/components/add-medical-prescription-dialog/common-medical-prescription/common-medical-prescription';
    import MedicalPrescriptionModal from '@/views-hospital/medical-prescription/components/add-medical-prescription-dialog/medical-prescription-modal/medical-prescription-modal';
    import EditMedicalAdviceTable
        from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/index.vue';
    import ChineseMedicalDialog from '@/views-hospital/medical-prescription/components/chinese-medical/chinese-medical-dialog.vue';
    import AddMedicalPrescriptionModalDialog from '@/views-hospital/medical-prescription/components/add-medical-prescription-dialog/medical-prescription-modal/add-medical-prescription-modal-dialog';
    import AddMedialDiagnosisDialog
        from '@/views-hospital/medical-prescription/components/add-medial-diagnosis-dialog/index.vue';
    import GoodsSelectDialog from 'src/views/layout/goods-select-dialog/index.vue';
    import MedicalPrescriptionAPI from '@/api/hospital/medical-prescription/index.js';
    import {
        TreatmentTypeEnum, AdviceRuleType, DoctorMedicalPrescriptionTypeEnum, MedicalAdviceTypeEnum, AdviceDispenseType,
    } from '@/views-hospital/medical-prescription/utils/constants';
    import { MedicalPrescriptionService } from '@/views-hospital/medical-prescription/model/index.js';
    import clone from 'utils/clone.js';
    import {
        createGUID, isNotNull,
    } from 'utils/index.js';
    import {
        mapActions, mapGetters,
    } from 'vuex';
    import {
        PharmacyTypeEnum, SearchSceneTypeEnum,
    } from 'views/common/enum.js';
    import {
        getFreqInfoByFirst, getSingleDosageUnitType,
    } from '@/views-hospital/medical-prescription/utils/format-advice.js';
    import {
        formatDate, parseTime,
    } from '@abc/utils-date';
    import {
        DiagnosisTypeEnum,
    } from '@/views-hospital/medical-prescription/model/medical-diagnosis.js';
    import SettingsAPI from 'api/settings.js';

    import { AdviceExaminationExtendSpec } from '@/views-hospital/medical-prescription/utils/constants';
    import {
        GoodsSubTypeEnum, GoodsTypeEnum, GoodsTypeIdEnum,
    } from '@abc/constants';
    import { CATEGORY_TYPE_ENUM } from 'src/views/common/goods-search/constants';
    import SettingAPI from 'api/settings.js';
    import { getDefaultPharmacy } from 'views/common/pharmacy.js';
    import {
        OutpatientChargeTypeEnum, SignatureTypeEnum,
    } from 'views/outpatient/constants.js';
    import MedicalPrescriptionOrderedProTable from 'views/layout/tables/table-medical-prescription-ordered/index.vue';
    import { debounce } from 'utils/lodash';
    import { FeeTypeNameEnum } from '@/views-hospital/beds/utils/constant';
    import ShebaoRestrictAPI from 'api/shebao-restrict';
    import HospitalMedicalInsuranceRestriction
        from '@/views-hospital/medical-prescription/components/add-medical-prescription-dialog/hospital-medical-insurance-restriction/index.vue';
    import { isAllowAddByAntimicrobialDrugManagement } from 'views/outpatient/utils';
    import AntimicrobialDrugManagementModal from 'views/outpatient/common/antimicrobial-drug-limit-modal';
    import { isDisabledGoods } from 'utils/validate';
    import { ValidateAdvice } from '@/views-hospital/medical-prescription/utils/validate-advice';
    import { EqConversionRuleEnum } from '@/common/constants/outpatient';
    import NeiMengPassPR from '@/pass-pharm-review';
    import { PassAuditStatusEnum } from '@/pass-pharm-review/constants';

    export default {
        components: {
            HospitalMedicalInsuranceRestriction,
            CommonMedicalPrescription,
            MedicalPrescriptionModal,
            EditMedicalAdviceTable,
            ChineseMedicalDialog,
            AddMedicalPrescriptionModalDialog,
            AddMedialDiagnosisDialog,
            GoodsSelectDialog,
            MedicalPrescriptionOrderedProTable,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            patientOrderId: {
                type: String,
                required: true,
            },
            patient: {
                type: Object,
                required: true,
            },
            copyIds: {
                type: Array,
                default() {
                    return [];
                },
            },
            doctor: {
                type: Object,
                default: () => ({}),
            },
            no: {
                type: [Number, String],
                default: 0,
            },
            nurseLevelAdvice: {
                type: [Object, null],
                default: null,
            },
            selectQuickItem: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            operateDepartmentId: {
                type: String,
            },
            operateFrom: {
                type: Number,
            },
        },
        data() {
            return {
                SearchSceneTypeEnum,
                GoodsTypeIdEnum,
                CATEGORY_TYPE_ENUM,
                userUseModelRule: 'USER_USE_MODEL_RULE',
                isShowMedicalPrescriptionModal: false,
                isShowChineseDialog: false,
                isShowAddMedicalPrescriptionModalDialog: false,
                isShowDiagnosisDialog: false,
                tabs: [
                    {
                        label: '常用医嘱',
                        value: 0,
                    },
                    {
                        label: '医嘱模板',
                        value: 1,
                    },
                    {
                        label: '已开医嘱',
                        value: 2,
                    },
                ],
                currentTab: 0,
                isShowExaminationDialog: false,
                isShowSingleExaminationDialog: false,
                isShowTransferHospitalDialog: false,
                isShowDischargeHospitalDialog: false,
                createLoading: false,
                postData: {
                    adviceGroups: [],
                    patientOrderId: '',
                    examApplySheetReqs: [],//检验医嘱需要传
                    operateDepartmentId: this.operateDepartmentId,
                    operateFrom: this.operateFrom,
                    surgeryReqs: [], // 手术申请单
                },
                diagnosisTreatmentType: TreatmentTypeEnum.UN_KNOW,
                curChineseGroupItem: null,
                defaultDiagnosisType: null,
                diagnosisTableData: [],
                currentTabChoice: undefined,
                commonAdviceLoading: false,
                modelAdviceGroups: [],
                categoryRange: [],
                mpList: [],
                hospitalVerifyRsp: {},
                showInsuranceRestriction: false,
                verifyLoading: true,
                isOpened: false,
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'isNeedShowModel',
                'pharmacyRuleList',
                'userInfo',
                'goodsConfig',
            ]),
            ...mapGetters('executeTime', ['allExecuteTimeList']),
            ...mapGetters('shebaoRestrict', ['restrictSwitch']),
            curPatientHospitalInfo() {
                return this.$abcPage.$store.curPatientHospitalInfo || {};
            },
            patientWardId() {
                return this.$abcPage.$store.currentPatientWardId;
            },
            piecesDefaultPharmacy() {
                return getDefaultPharmacy(this.pharmacyRuleList, {
                    wardAreaId: this.patientWardId,
                    goodsInfo: {
                        typeId: GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES,
                    },
                });
            },
            dialogVisible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            departmentId() {
                return this.doctor?.departmentId || '';
            },
            doctorId() {
                return this.userInfo.id || '';
            },
            isEmptyContent() {
                return !this.postData?.adviceGroups?.length ;
            },
            /**
             * @desc 保存模板过滤手写嘱托
             * <AUTHOR>
             * @date 2023-12-25 16:08:11
             */
            disabledSaveModelOperation() {
                const groups = this.postData.adviceGroups.filter((group) => {
                    const advices = group.advices.filter((advice) => {
                        return advice.createdType !== DoctorMedicalPrescriptionTypeEnum.WRITTEN && advice.adviceRule.isChecked;
                    });
                    return !!advices.length;
                });
                return this.disabledOperation || !groups.length;
            },
            disabledOperation() {
                return this.isEmptyContent || this.isNotCheckedAdviceTable;
            },
            isNotCheckedAdviceTable() {
                const {
                    isCheckAll, indeterminate,
                } = this.$refs?.medicalAdviceTable || {};
                return !isCheckAll && !indeterminate;
            },
            customStyles() {
                const width = Math.max($('#abc-container').outerWidth() - 104, 1360);
                // zxd 产品说 左右各预留 52px，多余出现横向滚动条
                return {
                    width: `${width}px`,
                    maxWidth: '1580px',
                };
            },
            /**
             * 费别
             * @return {string}
             */
            feeTypeName() {
                return this.$abcPage.$store.curPatientHospitalInfo?.feeTypeName;
            },
            insuranceRestrictionCount() {
                let count = 0;
                const {
                    behaviorVerifyLists, medicateVerifyLists,
                } = this.hospitalVerifyRsp;
                (behaviorVerifyLists || []).forEach((behaviorVerify) => {
                    if (Array.isArray(behaviorVerify.verifyDetails)) {
                        count += behaviorVerify.verifyDetails.length;
                    }
                });
                (medicateVerifyLists || []).forEach((medicateVerify) => {
                    if (Array.isArray(medicateVerify.verifyDetails)) {
                        count += medicateVerify.verifyDetails.length;
                    }
                });
                return count;
            },
            insuranceRestrictionResultCount() {
                let count = 0;
                const {
                    behaviorVerifyLists, medicateVerifyLists,
                } = this.hospitalVerifyRsp;
                let verifyList = [];
                (behaviorVerifyLists || []).forEach((behaviorVerify) => {
                    if (Array.isArray(behaviorVerify.verifyDetails)) {
                        verifyList = verifyList.concat(behaviorVerify.verifyDetails);
                    }
                });
                (medicateVerifyLists || []).forEach((medicateVerify) => {
                    if (Array.isArray(medicateVerify.verifyDetails)) {
                        verifyList = verifyList.concat(medicateVerify.verifyDetails);
                    }
                });
                for (const verifyItem of verifyList) {
                    if (!verifyItem.isDeal) {
                        count++;
                    }
                }
                return count;
            },
            isVerifyInsuranceRestriction() {
                const isShebaoSettle = this.feeTypeName === FeeTypeNameEnum.NATIONAL_MEDICAL_INSURANCE;
                // 如果关闭了控费开关，则不验证医保合规
                // 如果选择了仅医保审核，自费时不验证医保合规
                // 其余情况都要验证医保合规
                if (this.restrictSwitch === 0) return false;
                return !(this.restrictSwitch === 2 && !isShebaoSettle);
            },
        },
        watch: {
            'postData.adviceGroups': {
                handler(v) {
                    // 数据迁移：将 adviceRule.tagTypes 迁移到 advice.tagTypes
                    if (v && v.length > 0) {
                        const migratedGroups = MedicalPrescriptionService.migrateTagTypesToAdviceLevel(v);
                        if (JSON.stringify(migratedGroups) !== JSON.stringify(v)) {
                            this.postData.adviceGroups = migratedGroups;
                            return; // 避免重复触发
                        }
                    }

                    if (this.$abcSocialSecurity.isOpenSocial && this.isVerifyInsuranceRestriction && !this.isEmptyContent && typeof this._verifyAdvices === 'function') {
                        this._verifyAdvices(v);
                    }
                },
                immediate: true,
                deep: true,
            },
            copyIds: {
                handler(val) {
                    if (val) {
                        this.getDetailsByIds(val);
                    }
                },
                immediate: true,
                deep: true,
            },
        },
        created() {
            this._verifyAdvices = debounce(this.verifyAdvices, 250, true);

            this.$store.dispatch('initDispensingConfig');
            this.$store.dispatch('initProcessUsagesAvailable');
            this.$store.dispatch('executeTime/initExecuteTimeList');
            this.$store.dispatch('hospitalDoctor/initDoctorExaminationRemarks');
            this.$store.dispatch('hospitalDoctor/initDoctorInspectRemarks');
            this.$store.dispatch('hospitalDoctor/initDoctorTreatmentRemarks');
            this.getDiagnosisList();
            const userRule = localStorage.getItem(this.userUseModelRule);
            this.initCommonAdviceList(userRule === '2' ? true : false);
            this.fetchAddedMedicalPrescriptionData();
            this.$abcEventBus.$on('nmg-pass-pr-query', (data) => {
                const {
                    shortId, goodsName, drugQueryType,
                } = data;
                NeiMengPassPR.doRefDrug({
                    doctorId: this.curPatientHospitalInfo.doctorId,
                    doctorName: this.curPatientHospitalInfo.doctorName,
                    departmentId: this.curPatientHospitalInfo.departmentId,
                    departmentName: this.curPatientHospitalInfo.departmentName,
                    shortId,
                    goodsName,
                    drugQueryType,
                });
            }, this);
        },
        methods: {
            ...mapActions([
                'setUserUseRule',
            ]),
            async initCommonAdviceList(status) {
                this.isShowMedicalPrescriptionModal = status;
                if (this.isShowMedicalPrescriptionModal) {
                    this.commonAdviceLoading = true;
                    try {
                        await this.$abcPage.$store.fetchCommonAdviceList(this.currentClinic.clinicId);
                    } catch (e) {
                        console.log(e);
                    } finally {
                        this.commonAdviceLoading = false;
                    }

                }
            },
            openExaminationDialog(defaultCate, range) {
                this.currentTabChoice = defaultCate;
                this.isShowExaminationDialog = true;
                this.categoryRange = range;
            },
            async getDiagnosisList() {
                try {
                    const {
                        patientOrderId, outpatientOrderId = '',
                    } = this;
                    const list = await MedicalPrescriptionAPI.getDiagnosisList({
                        patientOrderId,
                        outpatientOrderId,
                    });
                    if (list?.length) {
                        this.diagnosisTableData = list;
                    }

                } catch (err) {
                    console.log(err);
                }
            },
            openDiagnosis() {
                this.isShowDiagnosisDialog = true;
            },
            /**
             * @desc 收集医嘱中药品的库存信息
             * <AUTHOR>
             * @date 2023-03-01 20:55:29
             */
            getGroupMedicineList(groups) {
                const goodsIdSet = new Set();
                groups.forEach((group) => {
                    group.advices.forEach((advice) => {
                        const { adviceRule } = advice;
                        adviceRule.items.forEach((item) => {
                            goodsIdSet.add(item.goodsId);
                        });
                    });
                });
                return Array.from(goodsIdSet);
            },
            /**
             * @desc 获取模板的库存信息,
             * <AUTHOR>
             * @date 2023-03-01 21:08:19
             * @return 返回查询到的一个goodsMap数据，key 为goodsId， value为goods
             */
            async fetchTemplateStock(goodsIdList) {
                const { data } = await SettingsAPI.commonPrescription.fetchPrescriptionTemplateStock({
                    goodsIds: goodsIdList,
                    wardAreaId: this.patientWardId,
                    sceneType: SearchSceneTypeEnum.hospital,
                });
                const goodsStockMap = new Map();
                if (data?.list?.length) {
                    data.list.forEach((goods) => {
                        if (!goodsStockMap.has(goods.id)) {
                            goods.goodsId = goods.id;
                            goodsStockMap.set(goods.id, goods);
                        }
                    });
                }
                return goodsStockMap;
            },
            async useModel(item) {
                if (item?.file?.adviceGroups?.length) {
                    item.file.adviceGroups.forEach((group) => {
                        group.advices.forEach((advice) => {
                            this.$set(advice.adviceRule, 'isChecked', false);
                        });
                    });
                    const modelGroups = MedicalPrescriptionService.handleModelGroup(item?.file?.adviceGroups || [], this.allExecuteTimeList);
                    const updateCopyGroupAdviceModelGroups = await this.updateCopyGroupAdvice(modelGroups);
                    // 校验西药和中成药的抗菌限制是否合规，规律掉不合规的提示
                    let filterAdviceGroups = this.filterCheckAntimicrobial(updateCopyGroupAdviceModelGroups);
                    // 数据迁移：将 adviceRule.tagTypes 迁移到 advice.tagTypes
                    filterAdviceGroups = MedicalPrescriptionService.migrateTagTypesToAdviceLevel(filterAdviceGroups);
                    this.postData.adviceGroups = this.postData.adviceGroups.concat(filterAdviceGroups);
                }
            },
            refreshModel() {
                this.$refs?.medicalModel?.refreshModel();
            },
            createModelMenu() {
                this.setMedicalPrescriptionModal(true);
                this.currentTab = 1;
                this.$nextTick(() => {
                    this.$refs?.medicalModel?.openModelMenu();
                });
            },
            setMedicalPrescriptionModal(status) {
                const rule = status ? '2' : '1';
                localStorage.setItem(this.userUseModelRule, rule);
                this.initCommonAdviceList(status);
            },
            // 批量开出检查检验项目
            async batchSelect(selectedList) {
                const { data } = await SettingAPI.commonPrescription.fetchPrescriptionTemplateStock({
                    sceneType: SearchSceneTypeEnum.hospital,
                    wardAreaId: this.patientWardId,
                    goodsIds: selectedList.map((item) => item.id),
                });
                const goodsList = data.list || [];
                selectedList.forEach((item) => {
                    const newGoods = goodsList.find((goodsItem) => item.id && goodsItem.id === item.id);
                    if (newGoods) {
                        Object.assign(item, newGoods);
                    }
                });
                this.$refs.medicalAdviceTable.selectItemByList(selectedList);
            },

            handleSelect(item) {
                this.$refs.medicalAdviceTable.selectItemByList([item]);
            },
            // 出院
            discharge(type,params) {
                console.log(type,params);
            },
            // 转科
            transfer(params) {
                console.log(params);
            },
            // 生成检查检验单
            createExamApplySheetId(params) {
                console.log(params);
            },
            saveModel() {
                this.$refs.medicalPrescriptionForm.validate((val) => {
                    const checkedGroups = this.getModelNeedValidateAdvice();
                    if (val) {
                        this.modelAdviceGroups = checkedGroups;
                        this.isShowAddMedicalPrescriptionModalDialog = true;
                    }
                },(formItem) => {
                    return !formItem.AbcFormItemGroup.$attrs.adviceRule.isChecked;
                });
            },
            preCheckDisableSell(adviceGroups) {
                const disabledSellGoodsNameList = [];
                for (let i = 0; i < adviceGroups.length; i++) {
                    const group = adviceGroups[i];
                    for (let j = 0; j < (group.advices || []).length; j++) {
                        const advice = group.advices[j];
                        for (let k = 0; k < (advice.adviceRule.items || []).length; k++) {
                            const item = advice.adviceRule.items[k];
                            const goods = {
                                productInfo: item.productInfo ? item.productInfo : item,
                            };
                            if (isDisabledGoods(goods).flag) {
                                disabledSellGoodsNameList.push(goods.productInfo.medicineCadn || goods.productInfo.name);
                            }
                        }
                    }
                }
                return disabledSellGoodsNameList;
            },
            /**
             * 提交前预检测抗菌用药是否符合规范
             * @return {boolean}
             */
            preCheckAntimicrobial(adviceGroups) {
                const westernItems = [];
                adviceGroups.forEach((group) => {
                    group.advices.forEach((advice) => {
                        const { adviceRule } = advice;
                        if (adviceRule.type === AdviceRuleType.WESTERN_MEDICINE && !!adviceRule.items?.[0]) {
                            westernItems.push(adviceRule.items[0]);
                        }
                    });
                });
                const {
                    antimicrobialDrugManagementData, employeeListByPractice,
                } = this.$store.getters;
                const filterFormItems = [];
                westernItems.forEach((item) => {
                    const isSuccess = isAllowAddByAntimicrobialDrugManagement(item, this.doctorId, antimicrobialDrugManagementData, employeeListByPractice);
                    if (!isSuccess) {
                        filterFormItems.push(item);
                    }
                });
                if (filterFormItems.length) {
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        new AntimicrobialDrugManagementModal({
                            list: filterFormItems, showViewCheckDoctorDetailBtn: true,
                        }).generateDialogAsync({ parent: this });
                    }, 100);
                    return false;
                }
                return true;
            },
            /**
             * 校验医嘱的库存是否充足
             * @param {Object[]} adviceGroups
             * @return {{flag: number, tips: string}} flag: 0:库存充足，1:库存不足
             */
            preCheckStockNotEnough(adviceGroups) {
                const noStockAdviceList = [];
                for (const adviceGroup of adviceGroups) {
                    const {
                        advices, type: groupType, dispenseType,
                    } = adviceGroup;
                    if (!advices.length) {
                        continue;
                    }
                    for (const advice of advices) {
                        const { adviceRule } = advice;
                        const { type: adviceRuleType } = adviceRule;
                        // 中药
                        if ([AdviceRuleType.CHINESE_MEDICINE_GRANULES, AdviceRuleType.CHINESE_MEDICINE_TABLETS].includes(adviceRuleType)) {
                            const {
                                items, dosageCount,
                            } = adviceRule;
                            for (const item of items) {
                                if (this.checkChineseAdviceStockNotEnough(item, dosageCount)) {
                                    noStockAdviceList.push(item.medicineCadn || item.name);
                                }
                            }
                        } else if (
                            [
                                AdviceRuleType.WESTERN_MEDICINE,
                                AdviceRuleType.MEDICINE_MATERIAL,
                                AdviceRuleType.SELF_PRODUCT,
                                AdviceRuleType.HEALTH_MEDICINE,
                                AdviceRuleType.HEALTH_FOOD,
                                AdviceRuleType.OTHER_PRODUCT,
                            ].includes(adviceRuleType)
                        ) {
                            // 西药、耗材、商品
                            const {
                                items,
                            } = adviceRule;
                            for (const item of items) {
                                if (this.checkWesternAdviceStockNotEnough(item, adviceRule, dispenseType, groupType)) {
                                    noStockAdviceList.push(item.medicineCadn || item.name);
                                }
                            }
                        }
                    }
                }
                return {
                    flag: noStockAdviceList.length,
                    tips: noStockAdviceList.join('、'),
                };
            },
            /**
             * 校验中药库存是否充足
             * @param {Object} item
             * @param {string | number} dosageCount
             * @return {boolean | number} true:库存不足，false:库存充足
             */
            checkChineseAdviceStockNotEnough(item, dosageCount) {
                // 自备不校验库存
                if (item.chargeFlag === OutpatientChargeTypeEnum.NO_CHARGE) return false;
                dosageCount = typeof dosageCount === 'string' || typeof dosageCount === 'number' ? parseInt(dosageCount) : 1;
                const {
                    noStocks,
                    medicineCadn,
                    name,
                    unitCount,
                    stockPieceCount,
                } = item;
                if (medicineCadn || name) {
                    const isShortage = (dosageCount * unitCount) > stockPieceCount;
                    return isDisabledGoods(item).flag || isShortage || noStocks;
                }
                return false;
            },
            /**
             * 校验西药、耗材、商品的库存是否充足
             * @param {Object} item
             * @param {Object} adviceRule
             * @param {number} dispenseType
             * @param {number} groupType
             * @return {boolean | number} true:库存不足，false:库存充足
             */
            checkWesternAdviceStockNotEnough(item, adviceRule, dispenseType, groupType) {
                // 自备不校验库存
                if (item.chargeFlag === OutpatientChargeTypeEnum.NO_CHARGE) return false;
                // 1.长期医嘱&西药&单次发药使用单次发药量
                // 2.其余全部使用总发药量
                let unitCount, unit;
                if (
                    groupType === MedicalAdviceTypeEnum.LONG_TIME &&
                    adviceRule.type === AdviceRuleType.WESTERN_MEDICINE &&
                    dispenseType === AdviceDispenseType.SINGLE
                ) {
                    unitCount = Number.isNaN(Number(adviceRule.singleDispenseCount)) ? adviceRule.singleDispenseCount : Number(adviceRule.singleDispenseCount);
                    unit = adviceRule.singleDispenseUnit;
                } else {
                    unitCount = Number.isNaN(Number(adviceRule.dosageCount)) ? adviceRule.dosageCount : Number(adviceRule.dosageCount);
                    unit = adviceRule.dosageUnit;
                }


                const goods = {
                    productInfo: item.productInfo || {
                        ...item,
                    },
                    stockPieceCount: item.stockPieceCount,
                    stockPackageCount: item.stockPackageCount,
                    unitCount,
                    unit,
                };
                return ValidateAdvice.isShortageWesternAdvice(goods).flag;
            },
            /**
             * 校验西药和中成药的抗菌限制是否合规，过滤掉不合规的提示
             */
            filterCheckAntimicrobial(adviceGroups) {
                const {
                    antimicrobialDrugManagementData, employeeListByPractice,
                } = this.$store.getters;
                const filterFormItems = [];
                const filterAdviceGroup = adviceGroups.filter((group) => {
                    group.advices = group.advices.filter((advice) => {
                        const { adviceRule } = advice;
                        const item = adviceRule.items?.[0] || {};
                        // 只判断西药和中成药
                        if (item.type === GoodsTypeEnum.MEDICINE && [GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine, GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM].includes(item.subType)) {
                            const isSuccess = isAllowAddByAntimicrobialDrugManagement(item, this.doctorId, antimicrobialDrugManagementData, employeeListByPractice);
                            if (!isSuccess) {
                                filterFormItems.push(item);
                                return false;
                            }
                        }
                        return true;
                    });
                    return group.advices.length;
                });
                if (filterFormItems.length) {
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        new AntimicrobialDrugManagementModal({
                            list: filterFormItems, showViewCheckDoctorDetailBtn: true,
                        }).generateDialogAsync({ parent: this });
                    }, 100);
                }
                return filterAdviceGroup;
            },
            preCheckDismounting(adviceGroups) {
                for (const adviceGroup of adviceGroups) {
                    for (const advice of (adviceGroup.advices || [])) {
                        const {
                            adviceRule, diagnosisTreatmentType,
                        } = advice;
                        if ([TreatmentTypeEnum.MATERIALS, TreatmentTypeEnum.MEDICINE].includes(diagnosisTreatmentType)) {
                            const {
                                items, dosageUnit, singleDispenseUnit, type,
                            } = adviceRule;
                            // 中药不判断是否拆零
                            if ([AdviceRuleType.CHINESE_MEDICINE_TABLETS, AdviceRuleType.CHINESE_MEDICINE_GRANULES].includes(type)) {
                                continue;
                            }
                            const {
                                pieceUnit, packageUnit,
                            } = items[0] || {};
                            if (dosageUnit) {
                                if (dosageUnit === pieceUnit && dosageUnit !== packageUnit) {
                                    adviceRule.dosageIsDismounting = 1;
                                } else {
                                    adviceRule.dosageIsDismounting = 0;
                                }
                            }
                            if (singleDispenseUnit) {
                                if (singleDispenseUnit === pieceUnit && singleDispenseUnit !== packageUnit) {
                                    adviceRule.singleDispenseIsDismounting = 1;
                                } else {
                                    adviceRule.singleDispenseIsDismounting = 0;
                                }
                            }
                        }
                    }
                }
            },
            createMedicalAdvice() {
                this.defaultDiagnosisType = null;
                this.$refs.medicalPrescriptionForm.validate(async (val) => {
                    if (val) {
                        this.createLoading = true;
                        try {
                            const disabledSellGoodsNameList = this.preCheckDisableSell(this.postData.adviceGroups);
                            if (disabledSellGoodsNameList.length) {
                                this.$alert({
                                    type: 'warn',
                                    title: '提示',
                                    content: `“${disabledSellGoodsNameList.filter((name) => !!name).join('、')}”已停用不可销售，请换用其他替代医嘱`,
                                });
                                return;
                            }

                            if (!this.preCheckAntimicrobial(this.postData.adviceGroups)) return;

                            // 校验库存
                            const { stockGoodsConfig } = this.goodsConfig;
                            if (stockGoodsConfig?.disableNoStockGoods === 1) {
                                const checkStockResult = this.preCheckStockNotEnough(this.postData.adviceGroups);
                                if (checkStockResult.flag) {
                                    this.$alert({
                                        type: 'warn',
                                        title: '提示',
                                        size: 'medium',
                                        content: `“${checkStockResult.tips}”库存不足不可开出，请换用其他替代医嘱`,
                                    });
                                    return;
                                }
                            }

                            // 校验发药拆零方式是否正确，如果不正确，则修正
                            this.preCheckDismounting(this.postData.adviceGroups);

                            this.postData.patientOrderId = this.patientOrderId;
                            this.createLoading = true;

                            // 第三方pass
                            const passAuditStatus = await NeiMengPassPR.doHospitalCheck({
                                adviceGroups: this.postData.adviceGroups,
                                doctorId: this.doctorId,
                                doctorName: this.userInfo.name,
                                departmentId: this.departmentId,
                                departmentName: this.doctor.departmentName,
                                patient: this.patient,
                                curPatientHospitalInfo: this.curPatientHospitalInfo,
                            });
                            if (passAuditStatus > PassAuditStatusEnum.PASS) {
                                return false;
                            }

                            this.postData.examApplySheetReqs = this.postData.examApplySheetReqs?.map((item) => {
                                return {
                                    ...item,
                                    deviceType: item.extendSpec === AdviceExaminationExtendSpec.DEFAULT || item.type === 2 ? item.deviceType : '',
                                    subType: Number(item.extendSpec),
                                };
                            });
                            console.log(this.postData);
                            debugger;
                            await MedicalPrescriptionAPI.batchCreate(this.postData);

                            // 上传医保合规结果
                            this.handleUpdateShebaoRestrictResult();


                            this.dialogVisible = false;
                            this.$emit('create');
                        } catch (e) {
                            if (e.code === 49998 || e.code === 49997) {
                                const title = e.code === 49998 ? '未下出院诊断' : '未下入院诊断';
                                this.defaultDiagnosisType = e.code === 49998 ? DiagnosisTypeEnum.OUT_HOSPITAL : DiagnosisTypeEnum.IN_HOSPITAL;
                                this.$confirm({
                                    type: 'warn',
                                    title,
                                    content: e.message,
                                    onClose: () => {
                                        this.isShowDiagnosisDialog = true;
                                    },
                                    showCancel: false,
                                });
                            }
                        } finally {
                            this.createLoading = false;
                        }
                    }
                });
            },
            openChineseDialog() {
                const pharmacyNo = this.piecesDefaultPharmacy?.no ?? '';
                const pharmacyName = this.piecesDefaultPharmacy?.name ?? '';
                this.curChineseGroupItem = MedicalPrescriptionService.getGroupItem(null);
                this.curChineseGroupItem.usage = '煎服';
                this.curChineseGroupItem.days = null;
                this.curChineseGroupItem.freq = '1日3次';
                this.curChineseGroupItem.freqInfo = getFreqInfoByFirst(this.allExecuteTimeList, this.curChineseGroupItem.freq, true);
                this.curChineseGroupItem.advices[0].diagnosisTreatmentType = TreatmentTypeEnum.MEDICINE;
                this.curChineseGroupItem.advices[0].adviceRule.type = AdviceRuleType.CHINESE_MEDICINE_TABLETS;
                this.curChineseGroupItem.advices[0].adviceRule.singleDosageCount = '每次150ml';
                this.curChineseGroupItem.advices[0].adviceRule.singleDosageUnit = '';
                this.curChineseGroupItem.advices[0].adviceRule.dailyDosage = '1日1剂';
                this.curChineseGroupItem.advices[0].adviceRule.pharmacyNo = pharmacyNo;
                this.curChineseGroupItem.advices[0].adviceRule.pharmacyName = pharmacyName;
                this.curChineseGroupItem.advices[0].adviceRule.pharmacyType = PharmacyTypeEnum.LOCAL_PHARMACY;

                this.isShowChineseDialog = true;
            },

            /**
             * @desc 手写嘱托
             * <AUTHOR>
             * @date 2023-12-14 19:51:04
             */
            handleAddHandWrittenAdvice() {
                const adviceGroup = MedicalPrescriptionService.getHandWrittenGroupItem();
                this.postData.adviceGroups.push(adviceGroup);
                this.scrollTableBottom();
            },

            /**
             * @desc 录入中药医嘱信息
             * <AUTHOR>
             * @date 2023-02-01 15:53:39
             * @params
             * @return
             */
            addChineseMedical(groupItem) {
                if (groupItem) {
                    this.postData.adviceGroups.push(groupItem);
                    this.curChineseGroupItem = null;
                    this.scrollTableBottom();
                }
            },
            /**
             * @desc 删除选中的医嘱信息
             * <AUTHOR>
             * @date 2023-02-01 21:48:08
             */
            handleDelete() {
                this.postData.adviceGroups = this.postData.adviceGroups.filter((group) => {
                    group.advices = group.advices.filter((advice) => {
                        return !advice.adviceRule.isChecked;
                    });
                    return group.advices.length;
                });
                this.$nextTick(() => {
                    this.checkExamApplySheetReqs();

                    // 清除多余的手术申请单
                    this.$refs.medicalAdviceTable.handeClearSurgeryApplyList();
                });
            },
            checkExamApplySheetReqs() {
                // 找出检查检验申请单
                const examAdvice = this.postData.adviceGroups.filter((item) => {
                    return item.advices[0].adviceRule.type === AdviceRuleType.ASSAY || item.advices[0].adviceRule.type === AdviceRuleType.INSPECTION;
                });
                // 检查和检验都没有需要删除所有申请单
                if (!examAdvice?.length) {
                    this.postData.examApplySheetReqs = [];
                } else {
                    // 过滤剩下的单子
                    this.postData.examApplySheetReqs = this.postData.examApplySheetReqs.filter((item) => {
                        return examAdvice?.find((i) => {
                            if (i.advices[0].adviceRule.type === AdviceRuleType.INSPECTION && item.extendSpec === AdviceExaminationExtendSpec.DEFAULT) {
                                return i.deviceType === item.deviceType && i.extendSpec === item.extendSpec && formatDate(i.startTime, 'YYYY-MM-DD') === item.planExecuteDate;
                            }
                            if (i.advices[0].adviceRule.type === AdviceRuleType.INSPECTION && item.extendSpec !== AdviceExaminationExtendSpec.DEFAULT) {
                                return i.extendSpec === item.extendSpec && formatDate(i.startTime, 'YYYY-MM-DD') === item.planExecuteDate;
                            }
                            return item.deviceType === '' && formatDate(i.startTime, 'YYYY-MM-DD') === item.planExecuteDate;
                        });
                    });
                }
            },
            /**
             * @desc 恢复 advice item checked为false
             * <AUTHOR>
             * @date 2023-03-13 22:58:29
             * @params
             * @return
             */
            initAdviceChecked() {
                this.postData.adviceGroups?.forEach((group) => {
                    group.advices.forEach((advice) => {
                        advice.adviceRule.isChecked = false;
                    });
                });
            },
            handleIsCheckedGroup(group) {
                return group.advices.findIndex((item) => {
                    return item.adviceRule.isChecked;
                }) > -1;
            },

            getCheckedAdvice() {
                const groups = clone(this.postData.adviceGroups).filter((group) => {
                    return this.handleIsCheckedGroup(group);
                });
                return groups;
            },
            /**
             * @desc TODO 这里后续需要修改为 保存模板时只需要传必填的数据，目前为只重置了不必要保存的数据
             * <AUTHOR>
             * @date 2023-12-25 11:33:36
             */
            getModelAdviceGroupData(adviceGroups) {
                return adviceGroups.map((group) => {
                    const { advices } = group;
                    advices.forEach((advice) => {
                        advice.adviceRule.items.forEach((item) => {
                            item.chargeFlag = OutpatientChargeTypeEnum.DEFAULT;
                            item.remark = item.remark?.replace(/【自备】/g, '') || '';
                        });
                    });
                    return {
                        ...group,
                        advices,
                    };
                });
            },
            // 保存涉及到保存分组的数据 需要筛选出没有被选中的分组数据
            getModelNeedValidateAdvice() {
                const groups = clone(this.postData.adviceGroups).filter((group) => {
                    group.advices = group.advices.filter((item) => {
                        return item.createdType !== DoctorMedicalPrescriptionTypeEnum.WRITTEN;
                    });
                    if (group.advices.length) {
                        return this.handleIsCheckedGroup(group);
                    }
                }).map((group) => {
                    group.advices = group.advices.filter((advice) => {
                        return advice.adviceRule.isChecked;
                    });
                    return group;
                });
                return this.getModelAdviceGroupData(groups);
            },
            /**
             * @desc 检查是否是西成药医嘱
             * <AUTHOR>
             * @date 2023-02-07 19:38:46
             * @params array
             * @return boolean
             */
            checkIsAllMedicineAdvice(groups) {
                const flag = groups.findIndex((group) => {
                    const groupFlag = group.advices?.findIndex((item) => {
                        return item.adviceRule.type !== AdviceRuleType.WESTERN_MEDICINE;
                    }) > -1;
                    return groupFlag;
                }) > -1;
                return !flag;
            },
            /**
             * @desc 检查是否已经分组
             * <AUTHOR>
             * @date 2023-02-07 20:13:29
             */
            checkIsGroup(group) {
                return group.advice?.length > 1;
            },
            /**
             * @desc 检查是否选中了多个分组
             * <AUTHOR>
             * @date 2023-02-07 20:15:45
             */
            checkMulGroups(groups) {
                const len = groups?.length || 0;
                let groupCount = 0;
                for (let i = 0; i < len ; i++) {
                    if (this.checkIsGroup(groups[i])) {
                        groupCount++;
                    }
                    if (groupCount > 1) {
                        break;
                    }
                }
                return groupCount > 1;
            },
            /**
             * @desc 创建分组
             */
            handleCreateGroup() {
                let checkedGroups = this.getCheckedAdvice();
                // 未选中医嘱
                if (checkedGroups?.length <= 1) {
                    if (checkedGroups.length === 1) {
                        const { advices = [] } = checkedGroups[0];
                        if (advices.length <= 1) {
                            this.$Toast({
                                message: '请选择多条医嘱',
                                type: 'error',
                            });
                        } else {
                            this.$Toast({
                                message: '请选择分组外的其他医嘱',
                                type: 'error',
                            });
                        }
                    } else {
                        this.$Toast({
                            message: '请选择多条医嘱',
                            type: 'error',
                        });
                    }
                    return;
                }
                // 选中了非药品的医嘱
                const isAllMedicine = this.checkIsAllMedicineAdvice(checkedGroups);
                if (!isAllMedicine) {
                    this.$Toast({
                        message: '仅中西成药医嘱支持分组',
                        type: 'error',
                    });
                    return;
                }
                // 选中的数据是否在两个分组
                const hasMulGroups = this.checkMulGroups(checkedGroups);
                if (hasMulGroups) {
                    this.$Toast({
                        message: '多个分组不支持合并',
                        type: 'error',
                    });
                }
                let curGroup = null; // 分组的数据
                checkedGroups = checkedGroups.filter((group) => {
                    if (this.checkIsGroup(group)) {
                        curGroup = group;
                        return false;
                    }
                    return true;
                });
                if (!curGroup) {
                    curGroup = checkedGroups.splice(0,1)[0];
                }
                // 选中了已分组 + 未分组的数据
                // 选中了都是未分组的数据
                checkedGroups.forEach((group) => {
                    if (group.keyId !== curGroup.keyId) {
                        curGroup.advices = curGroup.advices.concat(group.advices);
                    }
                });
                const newGroups = [];
                this.postData.adviceGroups.forEach((group) => {
                    if (!this.handleIsCheckedGroup(group)) {
                        newGroups.push(group);
                    } else {
                        if (group.keyId === curGroup.keyId) {
                            group.advices = curGroup.advices;
                            newGroups.push(group);
                        }
                    }
                });
                this.postData.adviceGroups = newGroups;
                this.initAdviceChecked();
            },
            checkIsAllSignleAdviceGroup(groups) {
                return groups.findIndex((group) => {
                    return !this.checkIsGroup(group);
                }) === -1;
            },

            /**
             * @desc 取消分组
             */
            handleCancelGroup() {
                const checkedGroups = this.getCheckedAdvice();
                // 未选中医嘱
                if (!checkedGroups?.length) {
                    this.$Toast({
                        message: '未选中医嘱',
                        type: 'error',
                    });
                    return;
                }
                // 选中了非药品的医嘱
                const isAllMedicine = this.checkIsAllMedicineAdvice(checkedGroups);
                if (!isAllMedicine) {
                    this.$Toast({
                        message: '仅中西成药医嘱支持分组',
                        type: 'error',
                    });
                    return;
                }
                const isAllSingle = this.checkIsAllSignleAdviceGroup(checkedGroups);
                if (isAllSingle) {
                    this.$Toast({
                        message: '未选中分组',
                        type: 'error',
                    });
                }
                const newGroups = [];
                this.postData.adviceGroups.forEach((group) => {
                    // 没有选中的group
                    if (!this.handleIsCheckedGroup(group) || group.advices?.length === 1) {
                        newGroups.push(group);
                    } else {
                        group.advices.forEach((item) => {
                            const tempGroup = MedicalPrescriptionService.getGroupItem();
                            Object.assign(tempGroup, group, {
                                advices: [item],
                                keyId: createGUID(),
                            });
                            newGroups.push(clone(tempGroup));
                        });
                    }
                });
                this.postData.adviceGroups = newGroups;
                this.initAdviceChecked();
            },
            /**
             * @desc 更新医嘱的数据
             * <AUTHOR>
             * @date 2023-03-01 21:41:36
             */
            async updateCopyGroupAdvice(groups) {
                // 收集goodsId
                const goodsIdList = this.getGroupMedicineList(groups);
                if (!goodsIdList?.length) return groups;
                // 查询模板药品库存，以及关联项目
                const goodsStockMap = await this.fetchTemplateStock(goodsIdList);
                // 替换新的库存信息
                groups.forEach((group) => {
                    group?.advices?.forEach((advice) => {
                        const { adviceRule } = advice;
                        const supportEqPieces = adviceRule.eqConversionRule === EqConversionRuleEnum.EQ_PIECES;
                        adviceRule.items = adviceRule?.items.map((goods) => {
                            const curNewGoods = goodsStockMap.get(goods.id);
                            if (curNewGoods) {
                                const supportEqCoefficient = supportEqPieces && curNewGoods.typeId === GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE;
                                const eqCoefficient = supportEqCoefficient ? curNewGoods.eqCoefficient : null;
                                const eqUnitCount = eqCoefficient ? (goods.eqUnitCount || goods.unitCount) : '';
                                const unitCount = eqUnitCount ? Number((eqUnitCount / eqCoefficient).toFixed(2)) : goods.unitCount;
                                // 拼接productInfo 主要是显示 药品的价格
                                const curGoods = Object.assign(goods, {
                                    id: curNewGoods.id,
                                    antibiotic: curNewGoods.antibiotic,
                                    dangerIngredient: curNewGoods.dangerIngredient,
                                    hospitalNeedExecutive: curNewGoods.hospitalNeedExecutive,
                                    packageUnit: curNewGoods.packageUnit,
                                    pieceUnit: curNewGoods.pieceUnit,
                                    pieceNum: curNewGoods.pieceNum,
                                    dismounting: curNewGoods.dismounting,
                                    medicineDosageNum: curNewGoods.medicineDosageNum,
                                    medicineDosageUnit: curNewGoods.medicineDosageUnit,
                                    componentContentNum: curNewGoods.componentContentNum,
                                    componentContentUnit: curNewGoods.componentContentUnit,
                                    stockPackageCount: curNewGoods.stockPackageCount,
                                    stockPieceCount: curNewGoods.stockPieceCount,
                                    displaySpec: curNewGoods.displaySpec,
                                    pharmacyNo: curNewGoods.pharmacyNo,
                                    pharmacyType: curNewGoods.pharmacyType,
                                    medicineCadn: curNewGoods.medicineCadn,
                                    name: curNewGoods.name,
                                    lastPackageCostPrice: curNewGoods.lastPackageCostPrice,
                                    type: curNewGoods.type,
                                    subType: curNewGoods.subType,
                                    typeId: curNewGoods.typeId,
                                    piecePrice: curNewGoods.piecePrice,
                                    packagePrice: curNewGoods.packagePrice,
                                    feeComposeList: curNewGoods.feeComposeList || [],
                                    medicalFeeGrade: curNewGoods.medicalFeeGrade,
                                    shebao: curNewGoods.shebao,
                                    children: curNewGoods.children || [],
                                    extendInfo: curNewGoods.extendInfo || {},
                                    disableSell: curNewGoods.disableSell,
                                    eqCoefficient,
                                    eqUnitCount,
                                    unitCount,
                                });
                                return Object.assign(curGoods, {
                                    productInfo: {
                                        ...curNewGoods,
                                    },
                                });
                            }
                            return goods;
                        }) || [];
                        adviceRule.singleDosageUnitType = getSingleDosageUnitType(adviceRule.singleDosageUnit, adviceRule.items[0]);
                    });
                });
                return groups;
            },
            /**
             * @desc 批量获取详情
             * <AUTHOR>
             * @date 2023/02/09 15:04:34
             * @param
             * @return
             */
            async getDetailsByIds(ids) {
                try {
                    const { data } = await MedicalPrescriptionAPI.getDetailsByIds(ids);
                    const { rows } = data;
                    const newGroupList = [];
                    rows.forEach((group) => {
                        const isLongAdvice = group.type === MedicalAdviceTypeEnum.LONG_TIME;

                        // 手写医嘱没有 items
                        const extendSpec = group.advices[0]?.adviceRule?.ruleItems?.[0]?.goodsExtendSpec || '0';
                        const deviceType = group.advices[0]?.adviceRule?.ruleItems?.[0]?.goodsExamDeviceType ?? '';
                        if (typeof deviceType === 'number') {
                            group.deviceType = String(deviceType);
                        }

                        const adviceList = [];
                        group.advices.forEach((advice) => {
                            const { adviceRule } = advice;

                            const tempItems = adviceRule?.ruleItems?.filter((item) => {
                                // item.type === 1 是作为关联项的医嘱，
                                // item.type === 0 是手动添加的医嘱
                                return !item.type;
                            })?.map((item) => {
                                const res = {
                                    ...item,
                                    id: item.goodsId,
                                    displaySpec: item.goodsSpec,
                                    medicineCadn: item.goodsName,
                                    name: item.goodsName,
                                    medicalInsurancePayType: item.medicalInsurancePayType,
                                };
                                if (!res.keyId) {
                                    res.keyId = createGUID();
                                }
                                return res;
                            }) || [];

                            const isConsultation = advice.diagnosisTreatmentType === TreatmentTypeEnum.CONSULTATION;
                            const isMedicine = adviceRule.type === AdviceRuleType.WESTERN_MEDICINE;
                            const isGranules = adviceRule.type === AdviceRuleType.CHINESE_MEDICINE_GRANULES;
                            const tempAdvice = {
                                adviceRule: {
                                    keyId: createGUID(),
                                    isChecked: false,
                                    astFlag: adviceRule.astFlag,
                                    dailyDosage: adviceRule.dailyDosage,
                                    // 药品医嘱都清空 天数
                                    days: isMedicine ? '' : adviceRule.days,
                                    deathTime: adviceRule.deathTime,
                                    // 药品医嘱都清空 发药数量
                                    dosageCount: isMedicine ? '' : adviceRule.dosageCount,
                                    dosageUnit: isMedicine ? '' : adviceRule.dosageUnit,
                                    dosageIsDismounting: isMedicine ? '' : adviceRule.dosageIsDismounting,
                                    examApplySheetId: adviceRule.examApplySheetId,
                                    isNeedProcess: adviceRule.isNeedProcess,
                                    items: tempItems,
                                    ruleItems: adviceRule?.ruleItems || [],
                                    name: adviceRule.name,
                                    pharmacyNo: adviceRule.pharmacyNo,
                                    pharmacyType: adviceRule.pharmacyType,
                                    eqConversionRule: isGranules ? (adviceRule.eqConversionRule ?? EqConversionRuleEnum.EQ_PIECES) : null,
                                    processInfo: adviceRule.processInfo,
                                    remark: isConsultation ? adviceRule.remark : '',
                                    singleDosageCount: adviceRule.singleDosageCount,
                                    singleDosageUnit: adviceRule.singleDosageUnit,
                                    singleDosageUnitType: adviceRule.singleDosageUnitType,
                                    stopLongAdviceTime: adviceRule.stopLongAdviceTime,
                                    consultationSheet: isConsultation ? {
                                        beginTime: '',
                                        diseaseDesc: '',
                                        isUrgent: 0,
                                        location: '',
                                        participants: [],
                                        preDiagnosis: [],
                                        purpose: '',
                                    } : null,
                                    applySheetSourceId: '',
                                    isStopBeforeStartLongAdvice: adviceRule.isStopBeforeStartLongAdvice,
                                    transferToDepartment: adviceRule.transferToDepartment,
                                    treatmentSites: adviceRule.treatmentSites,
                                    type: adviceRule.type,
                                    singleDispenseCount: isMedicine ? '' : adviceRule.singleDispenseCount,
                                    singleDispenseUnit: isMedicine ? '' : adviceRule.singleDispenseUnit,
                                    singleDispenseIsDismounting: isMedicine ? '' : adviceRule.singleDispenseIsDismounting,
                                },
                                diagnosisTreatmentType: advice.diagnosisTreatmentType,
                                createdType: advice.createdType,
                                tagTypes: adviceRule.tagTypes || [], // 将 adviceRule.tagTypes 迁移到 advice.tagTypes
                            };
                            adviceList.push(tempAdvice);
                        });

                        const isMedicineAdvice = group.advices?.[0]?.adviceRule?.type === AdviceRuleType.WESTERN_MEDICINE;
                        // 复制后的医嘱数据
                        const tempGroup = {
                            keyId: createGUID(),
                            advices: adviceList,
                            days: isMedicineAdvice ? '' : group.days,
                            freq: group.freq,
                            // 长期医嘱初始化首日执行时间
                            freqInfo: getFreqInfoByFirst(this.allExecuteTimeList, group.freq, isLongAdvice),
                            startTime: parseTime(new Date(), 'y-m-d h:i', true),
                            stopTime: '',
                            type: group.type,
                            usage: group.usage,
                            ivgtt: group.ivgtt,
                            ivgttUnit: group.ivgttUnit,
                            deviceType,
                            extendSpec,
                            tagTypes: [],
                            dispenseType: group.dispenseType,
                        };
                        newGroupList.push(tempGroup);
                    });
                    let adviceGroups = await this.updateCopyGroupAdvice(newGroupList);
                    // 校验西药和中成药的抗菌限制是否合规，规律掉不合规的提示
                    adviceGroups = this.filterCheckAntimicrobial(adviceGroups);
                    // 数据迁移：将 adviceRule.tagTypes 迁移到 advice.tagTypes
                    adviceGroups = MedicalPrescriptionService.migrateTagTypesToAdviceLevel(adviceGroups);
                    this.postData.adviceGroups = this.postData.adviceGroups.concat(adviceGroups);
                } catch (e) {
                    console.error(e);
                }
            },
            async fetchAddedMedicalPrescriptionData() {
                if (!this.patientOrderId) return;
                try {
                    const { data } = await MedicalPrescriptionAPI.getListById(this.patientOrderId);
                    const {
                        rows,
                    } = data;
                    this.mpList = rows;
                } catch (e) {
                    console.error(e);
                }
            },
            scrollTableBottom() {
                this.$nextTick(() => {
                    $('#abc-excel-table').scrollTop(99999);
                });
            },
            async verifyAdvices(adviceGroups) {
                try {
                    this.verifyLoading = true;
                    const adviceGroupsReq = adviceGroups.map((group) => {
                        const {
                            advices,
                            freq,
                            keyId,
                            startTime,
                            stopTime,
                            type,
                        } = group;
                        const groupReq = {
                            freq, keyId,
                        };
                        const beginDate = type === MedicalAdviceTypeEnum.LONG_TIME ? (startTime || '').split(' ')[0] : null;
                        const endDate = type === MedicalAdviceTypeEnum.LONG_TIME ? (stopTime || '').split(' ')[0] : null;

                        groupReq.advices = (advices || []).map((advice) => {
                            const { adviceRule } = advice;
                            const {
                                dailyDosage,
                                dosageCount,
                                keyId: adviceKeyId,
                                singleDosageCount,
                                singleDispenseCount,
                                items,
                                goodsIdMedicalInsurancePayTypes,
                                verifySignatureStatus,
                            } = adviceRule;
                            const adviceReq = {
                                dailyDosage,
                                dosageCount: dosageCount ? dosageCount : singleDispenseCount,
                                keyId: adviceKeyId,
                                singleDosageCount,
                                beginDate,
                                endDate,
                                verifySignatureStatus,
                            };

                            adviceReq.items = (items || []).map((item) => {
                                const {
                                    name,
                                    displayName,
                                    medicineCadn,
                                    goodsName,
                                    productInfo,
                                    feeComposeList,
                                    keyId: itemKeyId,
                                    type,
                                    subType,
                                    unitCount,
                                    eqCoefficient,
                                    eqUnitCount,
                                    unit,
                                    feeComposeType: feeComposeTypeItem,
                                    goodsId,
                                    verifySignatureStatus: itemVerifySignatureStatus,
                                } = item;
                                const {
                                    feeComposeType, shebaoPayMode, shebao, shebaoNationalCode,
                                } = productInfo || {};
                                const formatShebaoPayMode = shebaoPayMode === 2 ? 30 : shebaoPayMode === 1 ? 10 : shebaoPayMode === 0 ? shebaoPayMode : undefined;
                                const {
                                    nationalCode, payMode,
                                } = shebao || {};
                                const formatPayMode = payMode === 2 ? 30 : payMode === 1 ? 10 : 0;

                                const feeComposeListAdviceItem = (feeComposeList || []).map((feeCompose) => {
                                    const findFeeCompose = (goodsIdMedicalInsurancePayTypes || []).find((payType) => payType.goodsId === feeCompose.goodsId);
                                    const {
                                        medicalInsurancePayType, shebaoPayMode: feeComposeShebaoPayMode,
                                    } = findFeeCompose || {};
                                    const formatFeeComposeShebaoPayMode = feeComposeShebaoPayMode === 2 ? 30 : feeComposeShebaoPayMode === 1 ? 10 : 0;
                                    return {
                                        goodsId: feeCompose.goodsId,
                                        feeComposeType: feeCompose.feeComposeType,
                                        goodsShebaoPayMode: isNotNull(feeCompose.shebaoPayMode) ? feeCompose.shebaoPayMode : 0,
                                        keyId: feeCompose.id,
                                        name: feeCompose.displayName || feeCompose.name,
                                        shebaoCode: feeCompose.shebaoNationalCode,
                                        shebaoPayMode: isNotNull(medicalInsurancePayType) ? medicalInsurancePayType : formatFeeComposeShebaoPayMode,
                                        type: feeCompose.type,
                                        subType: feeCompose.subType,
                                        unit: feeCompose.unit,
                                        unitCount: feeCompose.unitCount,
                                    };
                                });

                                const findItem = (goodsIdMedicalInsurancePayTypes || []).find((payType) => payType.goodsId === goodsId);
                                const { medicalInsurancePayType } = findItem || {};
                                return {
                                    goodsId,
                                    feeComposeList: feeComposeListAdviceItem,
                                    feeComposeType: feeComposeType ?? feeComposeTypeItem,
                                    goodsShebaoPayMode: isNotNull(shebaoPayMode) ? shebaoPayMode : 0,
                                    shebaoPayMode: isNotNull(medicalInsurancePayType) ? medicalInsurancePayType : isNotNull(formatShebaoPayMode) ? formatShebaoPayMode : formatPayMode,
                                    keyId: itemKeyId,
                                    name: displayName || medicineCadn || goodsName || name,
                                    shebaoCode: isNotNull(shebaoNationalCode) ? shebaoNationalCode : isNotNull(nationalCode) ? nationalCode : null,
                                    subType,
                                    type,
                                    unit,
                                    unitCount: (eqCoefficient && eqUnitCount) ? eqUnitCount : unitCount,
                                    verifySignatures: itemVerifySignatureStatus ? [SignatureTypeEnum.SHEBAO_RESTRICT] : [],
                                };
                            });

                            return adviceReq;
                        });

                        return groupReq;
                    });

                    const hospitalVerifyReq = {
                        adviceGroups: adviceGroupsReq,
                        departmentId: this.departmentId,
                        patient: this.patient,
                    };

                    this.hospitalVerifyRsp = await ShebaoRestrictAPI.hospitalVerify(hospitalVerifyReq);
                } catch (e) {
                    console.error(e);
                } finally {
                    this.verifyLoading = false;
                }
            },
            changeShebaoPayMode(verifyItem) {
                const {
                    shebaoPayMode, adviceDetails,
                } = verifyItem;
                const {
                    adviceGroupId, adviceItemId, adviceRuleId, parentId, goodsId,
                } = adviceDetails?.[0] || {};
                const { adviceGroups } = this.postData;
                const adviceGroup = (adviceGroups || []).find((it) => it.keyId === adviceGroupId);
                if (adviceGroup) {
                    const { advices } = adviceGroup;
                    const advice = (advices || []).find((it) => it.adviceRule?.keyId === adviceRuleId);
                    if (advice) {
                        const { adviceRule } = advice;
                        const { goodsIdMedicalInsurancePayTypes } = adviceRule;
                        const findGoodsIdMedicalInsurancePayType = (goodsIdMedicalInsurancePayTypes || []).find((it) => it.goodsId === goodsId);
                        if (findGoodsIdMedicalInsurancePayType) {
                            findGoodsIdMedicalInsurancePayType.medicalInsurancePayType = shebaoPayMode;
                        } else {
                            if (Array.isArray(goodsIdMedicalInsurancePayTypes)) {
                                goodsIdMedicalInsurancePayTypes.push({
                                    goodsId, medicalInsurancePayType: shebaoPayMode,
                                });
                            } else {
                                adviceRule.goodsIdMedicalInsurancePayTypes = [{
                                    goodsId, medicalInsurancePayType: shebaoPayMode,
                                }];
                            }
                        }

                        if (parentId) {
                            const item = (adviceRule.items || []).find((it) => it.keyId === parentId);
                            if (item) {
                                const { feeComposeList } = item;
                                const feeCompose = (feeComposeList || []).find((it) => it.id === adviceItemId);
                                if (feeCompose) {
                                    this.$set(feeCompose, 'medicalInsurancePayType', shebaoPayMode);
                                }
                            }
                        } else {
                            const item = (adviceRule.items || []).find((it) => it.keyId === adviceItemId);
                            if (item) {
                                this.$set(item, 'medicalInsurancePayType', shebaoPayMode);
                            }
                        }
                    }
                }
            },
            onShebaoRestrictSignature(verifyItem) {
                const {
                    adviceDetails, isDeal,
                } = verifyItem;
                const {
                    adviceGroupId, adviceRuleId,
                } = adviceDetails?.[0] || {};
                const { adviceGroups } = this.postData;
                const adviceGroup = (adviceGroups || []).find((it) => it.keyId === adviceGroupId);
                if (adviceGroup) {
                    const { advices } = adviceGroup;
                    const advice = (advices || []).find((it) => it.adviceRule?.keyId === adviceRuleId);
                    if (advice) {
                        this.$set(advice.adviceRule, 'verifySignatureStatus', isDeal ? 1 : null);
                    }
                }
            },
            shebaoRestrictThreeTimesSignature(verifyItem) {
                const {
                    adviceDetails, isSignature,
                } = verifyItem;
                const {
                    adviceGroupId, adviceRuleId, adviceItemId,
                } = adviceDetails?.[0] || {};
                const { adviceGroups } = this.postData;
                const adviceGroup = (adviceGroups || []).find((it) => it.keyId === adviceGroupId);
                const { advices } = adviceGroup || {};
                const advice = (advices || []).find((it) => it.adviceRule?.keyId === adviceRuleId);
                if (advice) {
                    const { items } = advice.adviceRule || {};
                    const item = (items || []).find((it) => it.keyId === adviceItemId);
                    if (item) {
                        this.$set(item, 'verifySignatureStatus', isSignature ? 1 : null);
                    }
                }
            },
            viewF1() {
                document.dispatchEvent(
                    new window.KeyboardEvent('keydown', { keyCode: 112 }),
                );
            },
            async handleUpdateShebaoRestrictResult() {
                const {
                    medicateVerifyLists, behaviorVerifyLists,
                } = this.hospitalVerifyRsp;
                const arrDanger = [], arrWarn = [];
                (medicateVerifyLists || []).concat(behaviorVerifyLists).forEach((item) => {
                    if (item.verifyDetails) {
                        item.verifyDetails.forEach((it) => {
                            if (it.level === 'DANGER') {
                                arrDanger.push({
                                    ...it,
                                    dealType: item.dealType,
                                });
                            } else if (it.level === 'WARN') {
                                arrWarn.push({
                                    ...it,
                                    dealType: item.dealType,
                                });
                            }
                        });
                    }
                });
                const dangerWarnArr = arrDanger.concat(arrWarn);
                const restrictCount = dangerWarnArr.length;
                const restrictSettingCount = dangerWarnArr.filter((it) => !!it.isDeal).length;

                if (!restrictCount) return;

                const data = {
                    businessId: this.patientOrderId,
                    restrictRuleResultViews: [{
                        businessId: this.patientOrderId,
                        businessType: 2,
                        restrictCount,
                        restrictSettingCount,
                        restrictInfo: JSON.stringify({
                            keyId: this.patientOrderId,
                            dangerWarnArr,
                        }),
                    }],
                };
                await ShebaoRestrictAPI.updateVerifyResult(data);
            },
            handleClose() {
                this.isOpened = false;
                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    this.showInsuranceRestriction = false;
                }, 500);
            },
            handleOpen() {
                this.showInsuranceRestriction = true;
                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    this.isOpened = true;
                }, 0);
            },
        },
    };
</script>

<style lang="scss" scoped>
@import "src/styles/theme.scss";
@import 'src/styles/mixin.scss';

.add-medical-prescription_dialog--box {
    &-body {
        position: relative;
        display: flex;
        flex-direction: column;
        min-height: 628px;
        max-height: 890px;

        &-top {
            flex: 1;
            width: 100%;
            max-height: 632px;
            padding: 16px 24px 24px;

            &-title {
                display: flex;
                justify-content: space-between;
                width: 100%;
                height: 32px;

                &-left {
                    display: flex;
                    justify-content: flex-start;
                    height: 32px;
                }

                &-right {
                    display: flex;
                    justify-content: flex-end;
                    height: 32px;
                    margin-left: auto;
                }
            }

            &-info {
                min-height: 200px;
                max-height: 536px;
                margin-top: 16px;
            }
        }

        &-bottom {
            width: 100%;
            max-height: 258px;
            margin-top: auto;

            &-title {
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 100%;
                height: 36px;
                padding: 0 24px;
                background: #f8f9fb;
                box-shadow: inset 0 -1px 0 0 var(--abc-color-P6);

                &-tabs {
                    display: flex;

                    ::v-deep .abc-tabs {
                        border-bottom: none !important;
                    }

                    ::v-deep .abc-tabs-item {
                        font-size: 14px;
                    }
                }

                &-button {
                    display: flex;
                    align-items: center;
                    height: 36px;
                    margin-left: auto;
                    font-size: 14px;
                    line-height: 20px;
                    color: var(--abc-color-B1);
                    cursor: pointer;
                }
            }

            &-info {
                position: relative;
                width: 100%;
                max-height: 322px;
                overflow: hidden;
            }
        }
    }
}
</style>
